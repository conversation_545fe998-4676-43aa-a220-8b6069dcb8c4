from common.request import KeyDemo
from interface import author_platform_path

# 发稿平台
author_platform_login = {
    "account": "guokuo",
    "password": "123456"
}


# 发稿平台token
def author_token_api():
    res = KeyDemo.my_request('post',
                            url=author_platform_path.author_url + author_platform_path.author_api_path["login"],
                            json=author_platform_login)
    access_token = res["data"]["token"]

    headers_dict = {
        "headers": [
            {
                "Authorization": f"Bearer {access_token}"
            }
        ]
    }

    return headers_dict


class get_auth_info:
    pass
