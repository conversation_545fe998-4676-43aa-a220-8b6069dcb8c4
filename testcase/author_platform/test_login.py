from datetime import datetime, timedelta

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from interface import author_platform_path

# 这个引用不能删，否则关联不到缓存里的header
from testcase.author_platform.get_headers import headers

# 获取文件相对路径
# from testcase.Information.get_auth_info import token_api

# 测试数据
data_file_path = ReadJsonFileUtils.get_data_path("data/author_platform/article_management", "tag.json")
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典
data = {
    "account":"caojm",
    "password":"123456"
}
@allure.feature("自动发稿平台")
class TestLogin:
    @pytest.mark.smoke
    @pytest.mark.parametrize("login", data_item[0:1])
    def test_auto_login(self, login):
        # 自动发稿列表
        print("用例ID:{}".format(login['id']))
        print("用例名称:{}".format(login['name']))
        res = KeyDemo.my_request('post',
                                 url=author_platform_path.author_url + author_platform_path.author_api_path["login"],
                                 json=data)
        access_token = res["data"]["token"]
        headers_dict = {
            "headers": [
                {
                    "Authorization": f"Bearer {access_token}"
                }
            ]
        }
        print(res)
        return headers_dict
        # # 对比时间
        # current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # latest_date = res["data"][0]["createAt"]
        #
        # print(f"================{latest_date}================ ")
        # print(f"================" + str(current_time) + "================ ")
        # time_within_hours(latest_date, current_time, 2, "解析时间差超过 2 小时，请留意发稿平台新闻")

    # @pytest.mark.smoke
    # @pytest.mark.parametrize("articleListV2", data_item[1:2])
    # def test_auto_write_v2(self, articleListV2, headers):
    #     # 自动发稿列表
    #     print("用例ID:{}".format(articleListV2['id']))
    #     print("用例名称:{}".format(articleListV2['name']))
    #     res = KeyDemo.my_request('get',
    #                              url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
    #                                  "articleListV2"],
    #                              headers=headers,
    #                              params=articleListV2['parameters'])
    #
    #     # 对比时间
    #     current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    #     latest_date = res["data"][0]["create_time"]
    #
    #     print(f" ================{latest_date}================ ")
    #     print(f" ================" + str(current_time) + "================ ")
    #     time_within_hours(latest_date, current_time, 12, "解析时间差超过 12 小时，请留意发稿平台-贸易管制新闻")

if __name__ == '__main__':
    pytest.main()