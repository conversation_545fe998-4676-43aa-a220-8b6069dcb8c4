# -*- coding: utf-8 -*-

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import author_platform_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.author_platform.get_headers import headers

# 获取文件相对路径
# 测试数据
data_file_path = ReadJsonFileUtils.get_data_path("data/author_platform/article_management", "tag.json")
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典
@allure.feature("自动发稿平台")
class TestTag:
    @pytest.mark.smoke
    @pytest.mark.parametrize("tag", data_item[0:1])
    def test_tag_smoke(self, tag, headers):
        # 获取文章推荐标签属性
        print("用例ID:{}".format(tag['id']))
        print("用例名称:{}".format(tag['name']))
        res = KeyDemo.my_request('post',
                                 url=author_platform_path.author_url + author_platform_path.author_api_path["tag"],
                                 headers=headers,
                                 json=tag['parameters'])
        tags = res["data"]["tags"]
        exresult_code = tag['desired']["status"]
        equals(exresult_code, res["status"])
        return tags



    @pytest.mark.smoke
    @pytest.mark.parametrize("tagnews", data_item[1:2])
    def test_news_smoke(self, tagnews, headers):
        #tagnews['parameters']["tags"] = self.test_tag_smoke()[0]
        #根据标签推荐相关文章
        print("用例ID:{}".format(tagnews['id']))
        print("用例名称:{}".format(tagnews['name']))
        res = KeyDemo.my_request('get',
                                 url=author_platform_path.author_url + author_platform_path.author_api_path["tagnews"],
                                 headers=headers,
                                 params=tagnews['parameters'])
        exresult_code = tagnews['desired']["status"]
        equals(exresult_code, res["status"])
        print(res)




if __name__ == '__main__':
    pytest.main()