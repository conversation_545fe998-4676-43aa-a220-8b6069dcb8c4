from datetime import datetime, timedelta

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import time_within_hours, equals
from common.util.time_util import parse_and_compare_time
from interface import author_platform_path

# 这个引用不能删，否则关联不到缓存里的header
from testcase.author_platform.get_headers import headers

# 获取文件相对路径
# from testcase.Information.get_auth_info import token_api

# 测试数据
data_file_path = ReadJsonFileUtils.get_data_path("data/author_platform/article_management", "article_list.json")
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


def get_trade_list(headers):
    # 获取贸易管制文章列表

    res = KeyDemo.my_request('get',
                             url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
                                 "articleListV2"],
                             headers=headers,
                             params=data_item[0]['parameters'])

    # 获取 ID
    return res["data"][0]["id"]


def get_concept_stock_list(headers):
    # 获取概念股-公告文章列表
    res = KeyDemo.my_request('get',
                             url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
                                 "articleList"],
                             headers=headers,
                             params=data_item[3]['parameters'])

    # 获取 ID
    return res["data"][0]["esId"]


@allure.feature("自动发稿平台")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("articleList", data_item[0:1])
    def test_auto_write(self, articleList, headers):
        # 自动发稿列表
        print("用例name:{}".format(articleList['name']))
        res = KeyDemo.my_request('get',
                                 url=author_platform_path.author_url + author_platform_path.author_api_path[
                                     "articleList"],
                                 headers=headers,
                                 params=articleList['parameters'])

        # 对比时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        latest_date = res["data"][0]["createAt"]

        print(f"================{latest_date}================ ")
        print(f"=============={{" + str(current_time) + "}}================ ")
        time_within_hours(latest_date, current_time, 4, "解析时间差超过 4 小时，请留意发稿平台新闻")

    @pytest.mark.smoke
    @pytest.mark.parametrize("articleListV2", data_item[1:2])
    def test_auto_write_v2(self, articleListV2, headers):
        # 自动发稿列表
        print("用例name:{}".format(articleListV2['name']))
        res = KeyDemo.my_request('get',
                                 url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
                                     "articleListV2"],
                                 headers=headers,
                                 params=articleListV2['parameters'])

        # 对比时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        latest_date = res["data"][0]["create_time"]

        print(f" ================{latest_date}================ ")
        print(f" ================" + str(current_time) + "================ ")
        time_within_hours(latest_date, current_time, 12, "解析时间差超过 12 小时，请留意发稿平台-贸易管制新闻")

    @pytest.mark.smoke
    @pytest.mark.parametrize("articleListV2Edit", data_item[2:3])
    def test_auto_write_v2_edit(self, articleListV2Edit, headers):
        # 贸易管制文章id
        info_id = get_trade_list(headers)

        # 贸易管制文章详情

        res = KeyDemo.my_request('get',
                                 url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
                                     "articleDetail"] + str(info_id),
                                 headers=headers
                                 )

        # 获取期望的状态码
        exresult_code = data_item[0]['desired']["status"]
        # 断言实际状态码是否与期望状态码相等
        equals(exresult_code, res["status"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("articleListEdit", data_item[3:4])
    def t_auto_write_edit(self, articleListEdit, headers):
        # 贸易管制文章id
        esid = get_concept_stock_list(headers)


        res = KeyDemo.my_request('get',
                                 url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
                                     "articleDetailV1"] + str(esid),
                                 headers=headers
                                 )

        # 获取期望的状态码
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("outline", data_item[4:5])
    def test_automated_outline(self, outline, headers):
        # 获取写稿 outline


        res = KeyDemo.my_request('post',
                                 url=author_platform_path.author_url_ai + author_platform_path.author_api_path[
                                     "outline"],
                                 headers=headers,
                                 json=outline['parameters']
                                 )

        # 获取期望的状态码
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])
if __name__ == '__main__':
    pytest.main()
