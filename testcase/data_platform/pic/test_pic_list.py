import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals, less_than_or_equals, greater_than_or_equals
from interface import soya_path
from interface.soya_path import soya_api_path
from testcase.data_platform.test_soya_login import test_login_api_T, TestSoya

data_file_path = ReadJsonFileUtils.get_data_path("data/soya", "pic.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典

@allure.feature("图库")
class TestDemo:

    # 新增图库校验
    @pytest.mark.smoke
    @pytest.mark.parametrize("picList", data_item[0:1])
    def test_pic_list(self, picList):
        # 打印用例ID和名称到报告中显示

        res = KeyDemo.my_request('get',
                                 url=soya_path.data_utl + soya_api_path["picList"],
                                 params=picList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["status"]
        act_count = res["data"]["total"]
        equals(exresult_code, res["return_code"])
        greater_than_or_equals(act_count, 10000)


if __name__ == '__main__':
    pytest.main()
