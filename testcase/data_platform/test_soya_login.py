import hashlib

import allure
import pytest

from common.request import KeyDemo
from common.util.assert_type import equals
from interface import soya_path

# soya登录
soya_login = {
    "account": "***********",
    "password": "f939b4c30c9d6915ac9a185d608e8244",
    "verifyCode": "",
    "token": ""
}

url = soya_path.soya_url + soya_path.soya_api_path["login"]


def test_login_api_T():
    # 将各个部分拼接成字符串
    str_to_hash = soya_login["account"] + soya_login["password"] + soya_login["verifyCode"] + 'mySecret'

    # 使用 hashlib 生成 MD5 哈希
    token = hashlib.md5(str_to_hash.encode()).hexdigest()
    soya_login["token"] = token
    res = KeyDemo.my_request('post',
                             url=url,
                             json=soya_login)

    return res["data"]["token"]


@allure.feature("soya（管理后台） 登录")
class TestSoya:

    @pytest.mark.smoke
    def test_login_api(self):
        # 将各个部分拼接成字符串
        str_to_hash = soya_login["account"] + soya_login["password"] + soya_login["verifyCode"] + 'mySecret'

        # 使用 hashlib 生成 MD5 哈希
        token = hashlib.md5(str_to_hash.encode()).hexdigest()
        soya_login["token"] = token
        res = KeyDemo.my_request('post',
                                 url=url,
                                 json=soya_login)

        equals(0, res["ret"])


if __name__ == '__main__':
    pytest.main()
