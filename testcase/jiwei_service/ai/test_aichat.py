import json
import logging
import allure
import pytest
import uuid
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import assert_no_error_indicators_in_response
from interface import service_path
from testcase.Information.news.get_headers import ai_headers

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/ai", "test_data_ai.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("jiweiGPT对话框-问候语")
class TestAiChat:
    @pytest.mark.smoke
    def test_ai_chat(self, ai_headers):

        res = KeyDemo.my_request('get',
                                url=service_path.service_test_ai + service_path.service_interfacePath["ai"],
                                headers=ai_headers,
                                params=data_item[0]['parameters'])
        exresult_errmsg = data_item[0]['desired']["errmsg"][:2]
        assert exresult_errmsg == "Hi"

    @pytest.mark.smoke
    @pytest.mark.parametrize("aiConversation", data_item[1:2])
    def t_ai_conversation(self, aiConversation, ai_headers):
        # Generate new UUID for each test run
        parameters = aiConversation['parameters'].copy()
        parameters['message']['uuid'] = str(uuid.uuid4())
                            
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_ai + service_path.service_interfacePath["conversation"],
                                headers=ai_headers,
                                json=parameters)

        assert_no_error_indicators_in_response(res, f"Test failed for parameters: {parameters}")


if __name__ == '__main__':
    pytest.main()
