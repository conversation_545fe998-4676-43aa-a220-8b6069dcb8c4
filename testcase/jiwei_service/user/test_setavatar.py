import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
from testcase.jiwei_service.user.token import get_token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/user", "test_data_setavatar.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("我的相关 user1【上传图片】")
class TestSetavatar:
    @pytest.mark.smoke
    def test_setavatar_smoke_api(self):
        data_item[0]['parameters']["token"] = get_token_api()[0]
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_jobs + service_path.service_interfacePath["setavatar"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
