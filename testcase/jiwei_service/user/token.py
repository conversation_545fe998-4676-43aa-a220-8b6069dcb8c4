from common.request import KeyDemo
from interface import service_path, Information_path

service_account_xiaoxiao = {
    "login_platform": 1,
    "password": "0d5bd268931ebca0753acb2ff54a40f3",
    "phone": "***********",
    "source": "pc",
    "token": "",
    "type": "3",
}


# token值和用户作者author_id
def get_token_api():
    res = KeyDemo.my_request('post', url=service_path.service_test_url + service_path.service_interfacePath["login"],
                            params=service_account_xiaoxiao)
    author_id = res["data"]["userInfo"]["id"]
    token = res["data"]["token"]
    return token, author_id
