import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import *
from interface import service_path, Information_path

# 获取文件相对路径
from testcase.Information.news.get_headers import service_headers

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/business_insights", "test_bi.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("企业洞察")
class TestBiSearch:
    @pytest.mark.smoke
    def test_bi_search(self, service_headers):

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["biSearch"],
                                headers=service_headers,
                                params=data_item[0]['parameters'])
        actual_results = res["data"][0]["regStatus"]
        desired_result = data_item[0]['desired']["ret"]

        string_equals(actual_results, desired_result)


if __name__ == '__main__':
    pytest.main()
