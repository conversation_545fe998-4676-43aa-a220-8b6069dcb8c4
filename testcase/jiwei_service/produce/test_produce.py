import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path
from testcase.jiwei_service.news.test_vipfeed import TestVipfeed, get_vipfeed_id
from testcase.jiwei_service.user.token import get_token_api

# 获取文件相对路径
data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/produce", "test_data_produce.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("新闻水印【给新闻附件增加用户水印】")
class TestProduce:
    @pytest.mark.smoke
    def test_produce_smoke_api(self):
        data_item[0]['parameters']["token"] = get_token_api()[0]
        data_item[0]['parameters']["news_id"] = get_vipfeed_id()
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["produce"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])

    @pytest.mark.all
    @pytest.mark.parametrize("produce", data_item[1:])
    def test_produce_api(self, produce):
        if produce['id'] == "produce【token=null】" or produce['id'] == "produce【token=error】" or produce[
            'id'] == "produce【token=lack】":
            produce['parameters']["news_id"] = get_vipfeed_id()
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["produce"],
                                    params=produce['parameters'])
            exresult_code = produce['desired']["errno"]
            equals(exresult_code, res["errno"])
        else:
            produce['parameters']["token"] = get_token_api()[0]
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["produce"],
                                    params=produce['parameters'])
            exresult_code = produce['desired']["errno"]
            equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
