import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
from testcase.jiwei_service.user.token import get_token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_viprecord.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("资讯详情相关接口【资讯改版-VIP浏览记录】")
class TestViprecord:
    @pytest.mark.smoke
    def test_viprecord_smoke_api(self):
        data_item[0]['parameters']["token"] = get_token_api()[0]
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["viprecord"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])

    @pytest.mark.all
    @pytest.mark.parametrize("viprecord", data_item[1:])
    def test_viprecord_api(self, viprecord):
        if viprecord['id'] == "viprecord【token=null】" or viprecord['id'] == "viprecord【token=error】" or viprecord[
            'id'] == "viprecord【token=lack】":
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["viprecord"],
                                    params=viprecord['parameters'])
            exresult_code = viprecord['desired']["errno"]
            equals(exresult_code, res["errno"])

        else:
            viprecord['parameters']["token"] = get_token_api()[0]
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["viprecord"],
                                    params=viprecord['parameters'])
            exresult_code = viprecord['desired']["errno"]
            equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
