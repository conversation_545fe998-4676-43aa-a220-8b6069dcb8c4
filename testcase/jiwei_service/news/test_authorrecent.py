import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from interface import service_path
from testcase.jiwei_service.news.test_newsdetail import TestNewsdetail

# 获取文件相对路径
data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_authorrecent.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)


def is_special_case(case_id):
    special_cases = {
        "authorrecentauthor_id=null": True,
        "authorrecentauthor_id=error": True,
        "authorrecentauthor_id=lack": True
    }
    return case_id in special_cases


@allure.feature("资讯详情相关接口【获取作者资讯】")
class TestAuthorrecent:
    data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典

    def setup_method(self, method):
        self.base_url = f"{service_path.service_test_jobs}{service_path.service_interfacePath['authorrecent']}"

    def call_api(self, parameters):
        try:
            return KeyDemo.my_request('post', url=self.base_url, params=parameters)
        except Exception as e:
            print("API调用失败")
            raise

    @pytest.mark.smoke
    def test_authorrecent_smoke_api(self):
        data = self.data_item[0]
        # print(f"用例ID:{data['id']}")#
        data['parameters']["author_id"] = 5183558
        res = self.call_api(data['parameters'])
        assert res["errno"] == data["desired"][
            "errno"], f"Expected error code {data['desired']['errno']}, got {res['errno']}"
        assert res["errmsg"] == data["desired"][
            "errmsg"], f"Expected error message {data['desired']['errmsg']}, got {res['errmsg']}"

    @pytest.mark.all
    @pytest.mark.parametrize("authorrecent", data_item[1:])
    def test_authorrecent_api(self, authorrecent):
       # print(f"用例ID:{authorrecent['id']}")
        if is_special_case(authorrecent['id']):
            res = self.call_api(authorrecent['parameters'])
        else:
            authorrecent['parameters']["author_id"] = 5183558
            res = self.call_api(authorrecent['parameters'])
        assert res["errno"] == authorrecent["desired"][
            "errno"], f"Expected error code {authorrecent['desired']['errno']}, got {res['errno']}"
        assert res["errmsg"] == authorrecent["desired"][
            "errmsg"], f"Expected error message {authorrecent['desired']['errmsg']}, got {res['errmsg']}"


if __name__ == '__main__':
    pytest.main()
