import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
from testcase.jiwei_service.news.test_feedstream import TestFeedstream

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_newsv3.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("资讯详情相关接口【资讯搜索接口】")
class TestNewsv3:
    @pytest.mark.smoke
    def test_newsv3_smoke_api(self):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["newsv3"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])

    @pytest.mark.all
    @pytest.mark.parametrize("newsv3", data_item[1:])
    def test_newsv3_api(self, newsv3):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["newsv3"],
                                params=newsv3['parameters'])
        exresult_code = newsv3['desired']["errno"]
        equals(exresult_code, res["errno"])
        print(res)


if __name__ == '__main__':
    pytest.main()
