import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import string_equals, equals
from interface import service_path

# 获取文件相对路径
data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_feedstream.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


def get_news_id():
    res = KeyDemo.my_request('post',
                            url=service_path.service_test_url + service_path.service_interfacePath["feedstream"],
                            params=data_item[0]['parameters'])
    news_id = res["data"][1]["news_id"]

    return news_id


@allure.feature("资讯详情相关接口【获取feed流接口】")
class TestFeedstream:
    @pytest.mark.smoke
    @pytest.mark.parametrize("feedstream", data_item[0:1])

    def test_feedstream_smoke_api(self, feedstream):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["feedstream"],
                                params=feedstream['parameters'])
        exresult_code = feedstream['desired']["errno"]
        string_equals(exresult_code, res["errno"])

    # @pytest.mark.all
    # @pytest.mark.parametrize("feedstream", data_item[1:])
    # def test_feedstreamv2_api(self, feedstream):
    #     res = KeyDemo.my_request('post',
    #                             url=service_path.service_test_url + service_path.service_interfacePath["feedstream"],
    #                             params=feedstream['parameters'])
    #     exresult_code = feedstream['desired']["errno"]
    #     equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
