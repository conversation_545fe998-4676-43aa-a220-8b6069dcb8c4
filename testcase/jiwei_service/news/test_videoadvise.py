import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
from testcase.jiwei_service.news.test_feedstream import TestFeedstream

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_videoadvise.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("资讯详情相关接口【资讯改版-视频推荐】")
class TestVideoadvise:
    @pytest.mark.smoke
    def test_videoadvise_smoke_api(self):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["videoadvise"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])

    @pytest.mark.all
    @pytest.mark.parametrize("videoadvise", data_item[1:])
    def test_videoadvise_api(self, videoadvise):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["videoadvise"],
                                params=videoadvise['parameters'])
        exresult_code = videoadvise['desired']["errno"]
        equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
