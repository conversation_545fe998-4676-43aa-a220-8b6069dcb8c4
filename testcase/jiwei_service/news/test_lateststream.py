import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_lateststream.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("资讯详情相关接口【获取最新资讯】")
class TestLateststream:
    @pytest.mark.smoke
    def test_lateststream_smoke_api(self):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_jobs + service_path.service_interfacePath["lateststream"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])

    @pytest.mark.all
    @pytest.mark.parametrize("lateststream", data_item[1:])
    def test_lateststream_api(self, lateststream):
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_jobs + service_path.service_interfacePath["lateststream"],
                                params=lateststream['parameters'])
        exresult_code = lateststream['desired']["errno"]
        equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
