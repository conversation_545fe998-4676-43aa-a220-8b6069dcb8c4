import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
from testcase.jiwei_service.news.test_feedstream import TestFeedstream, get_news_id

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_newsdetail.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("资讯详情相关接口【获取资讯详情接口】")
class TestNewsdetail:
    @pytest.mark.smoke
    def test_newsdetail_smoke_api(self):
        data_item[0]['parameters']["news_id"] = get_news_id()
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["newsdetail"],
                                params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["errno"]
        equals(exresult_code, res["errno"])

    @pytest.mark.all
    @pytest.mark.parametrize("newsdetail", data_item[1:])
    def test_newsdetail_api(self, newsdetail):
        if newsdetail['id'] == "newsdetail【news_id=null】" or newsdetail['id'] == "newsdetail【news_id=error】" or \
                newsdetail['id'] == "newsdetail【news_id=lack】":
            res = KeyDemo.my_request('post', url=service_path.service_test_url + service_path.service_interfacePath[
                "newsdetail"], params=newsdetail['parameters'])
            exresult_code = newsdetail['desired']["errno"]
            equals(exresult_code, res["errno"])
        else:
            newsdetail['parameters']["news_id"] = get_news_id()
            res = KeyDemo.my_request('post', url=service_path.service_test_url + service_path.service_interfacePath[
                "newsdetail"], params=newsdetail['parameters'])
            exresult_code = newsdetail['desired']["errno"]
            equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
