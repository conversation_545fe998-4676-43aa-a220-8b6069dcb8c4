import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取文件相对路径
from testcase.jiwei_service.user.token import get_token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_vipfeed.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


def get_vipfeed_id():
    data_item[0]['parameters']["token"] = get_token_api()[0]
    res = KeyDemo.my_request('post',
                            url=service_path.service_test_url + service_path.service_interfacePath["vipfeed"],
                            params=data_item[0]['parameters'])
    vipnews_id = res["data"]["list"][0]["news_id"]
    return vipnews_id


@allure.feature("资讯详情相关接口【资讯改版-VIP资讯流】")
class TestVipfeed:
    @pytest.mark.smoke
    def test_vipfeed_smoke_api(self):
        data_item[0]['parameters']["token"] = get_token_api()[0]
        res = KeyDemo.my_request('post',
                                url=service_path.service_test_url + service_path.service_interfacePath["vipfeed"],
                                params=data_item[0]['parameters'])
        # vipnews_id = res["data"]["list"][0]["news_id"]
        if res["data"]["list"][0]["is_vip"] == 1:
            # print("是vip咨询")
            exresult_code = data_item[0]['desired']["errno"]
            equals(exresult_code, res["errno"])

        else:
            print("此资讯非vip")

    @pytest.mark.all
    @pytest.mark.parametrize("vipfeed", data_item[1:])
    def test_vipfeed_api(self, vipfeed):

        if vipfeed['id'] == "vipfeed【token=null】" or vipfeed['id'] == "vipfeed【token=error】" or vipfeed[
            'id'] == "vipfeed【token=lack】":
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["vipfeed"],
                                    params=vipfeed['parameters'])
            exresult_code = vipfeed['desired']["errno"]
            equals(exresult_code, res["errno"])

        else:
            vipfeed['parameters']["token"] = get_token_api()[0]
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["vipfeed"],
                                    params=vipfeed['parameters'])
            exresult_code = vipfeed['desired']["errno"]
            equals(exresult_code, res["errno"])


if __name__ == '__main__':
    pytest.main()
