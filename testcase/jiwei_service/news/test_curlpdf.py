import logging
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path
from testcase.jiwei_service.news.test_feedstream import TestFeedstream, get_news_id

# 获取文件相对路径
data_file_path = ReadJsonFileUtils.get_data_path("data/jiwei_service/news", "test_data_curlpdf.json")

# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')

@allure.feature("资讯详情相关接口【请求资讯详情PDF附件接口】")
class TestCurlpdf:
    @pytest.mark.smoke
    def test_curlpdf_smoke_api(self):
        """测试smoke接口"""
        self._test_curlpdf_api(data_item[0])

    @pytest.mark.all
    @pytest.mark.parametrize("curlpdf", data_item[1:])
    def test_curlpdf_api(self, curlpdf):
        """测试all接口"""
        self._test_curlpdf_api(curlpdf)

    def _test_curlpdf_api(self, curlpdf):
        """测试curlpdf接口"""
        if curlpdf['id'] in ["newsdetail【news_id=null】", "newsdetail【news_id=error】", "newsdetail【news_id=lack】"]:
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["curlpdf"],
                                    params=curlpdf['parameters'])
        else:
            curlpdf['parameters']["news_id"] = get_news_id()
            res = KeyDemo.my_request('post',
                                    url=service_path.service_test_url + service_path.service_interfacePath["curlpdf"],
                                    params=curlpdf['parameters'])
        # 如果错误码为500001表示资讯未上传PDF，这是预期行为，不需要额外断言
        # 否则验证错误码是否符合预期
        if res["errno"] != 500001:
            exresult_code = curlpdf['desired']["errno"]
            equals(exresult_code, res["errno"])