from common.request import KeyDemo
from interface import Information_path

infoplat_login = {
    "username": "e8a83fcb",
    "password": "2c13b4d3",
    "client_id": "3",
    "client_secret": "mmwGiXLYrr0XcSFXSTst4sDSgSRHoMx16t12gGNE",
    "scope": "eip"
}


class Token:
    # token值和用户作者author_id
    def token_api(self):
        res = KeyDemo.my_request('post', url=Information_path.Information_login,
                                params=infoplat_login)
        access_token = res["data"]["token"]

        headers_dict = {
            "headers": [
                {
                    "Authorization": f"Bearer {access_token}"
                }
            ]
        }

        return headers_dict


class get_auth_info:
    pass

