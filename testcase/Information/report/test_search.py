import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path

# 获取文件相对路径
from testcase.Information.user.get_auth_info import Token

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/report", "test_data_search.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


def get_report_id():
    # 打印用例ID和名称到报告中显示
    head = Token().token_api()
    res = KeyDemo.my_request('get',
                            url=Information_path.Information_pro_url + Information_path.Information_path["search"],
                            headers=head['headers'][0], params=data_item[0]['parameters'])
    report_id = res["data"]["list"][0]["id"]
    return report_id


@allure.feature("Report API【ReportSearch 图文报告列表】")
class TestSearch:

    @pytest.mark.smoke
    def test_search_smock_api(self):
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_pro_url + Information_path.Information_path["search"],
                                headers=head['headers'][0], params=data_item[0]['parameters'])
        # report_id = res["data"]["list"][0]["id"]
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])

    @pytest.mark.all
    @pytest.mark.parametrize("search", data_item[1:])
    def test_search_api(self, search):
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_pro_url + Information_path.Information_path["search"],
                                headers=head['headers'][0], params=search['parameters'])
        exresult_code = search['desired']["status"]
        equals(exresult_code, res["status"])


if __name__ == '__main__':
    pytest.main()
