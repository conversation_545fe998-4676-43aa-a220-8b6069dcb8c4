import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path

# 获取文件相对路径
from testcase.Information.report.test_search import TestSearch, get_report_id
from testcase.Information.user.get_auth_info import Token

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/report", "test_data_url.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典



@allure.feature("Report API【ReportSearch 图文报告内容】")
class TestUrl:

    @pytest.mark.smoke
    def test_url_smock_api(self):
        report = get_report_id()  # "3bDxVk/url"
        report_id = report + "/url"
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get', url=Information_path.Information_pro_url + Information_path.Information_path[
            "url"] + report_id, headers=head['headers'][0], params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])

    @pytest.mark.all
    @pytest.mark.parametrize("url", data_item[1:])
    def test_url_api(self, url):
        report = get_report_id()  # "3bDxVk/url"
        report_id = report + "/url"
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get', url=Information_path.Information_pro_url + Information_path.Information_path[
            "url"] + report_id, headers=head['headers'][0], params=url['parameters'])
        exresult_code = url['desired']["status"]
        equals(exresult_code, res["status"])


if __name__ == '__main__':
    pytest.main()
