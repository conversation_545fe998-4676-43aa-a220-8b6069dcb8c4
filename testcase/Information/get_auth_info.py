from common.request import KeyDemo
from interface import Information_path, service_path

# 信息平台登录信息
from testcase.jiwei_service.user.token import get_token_api

INFOPLAT_LOGIN_INFO = {
    "username": "e8a83fcb",
    "password": "2c13b4d3",
    "client_id": "3",
    "client_secret": "mmwGiXLYrr0XcSFXSTst4sDSgSRHoMx16t12gGNE",
    "scope": "eip"
}


def token_api() -> dict:
    """获取信息平台的令牌。"""
    try:
        response = KeyDemo.my_request('post', url=Information_path.Information_login, params=INFOPLAT_LOGIN_INFO)
        access_token = response["data"]["token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        return {"headers": [headers]}
    except Exception as e:
        print(f"Error obtaining token: {e}")
        return None


def ai_token_api() -> dict:
    """获取集微网GPT AI的令牌。"""
    try:
        response = get_token_api()
        jiwei_token = response[0]
        headers = {"Authorization": f"Bearer {jiwei_token}"}

        ai_token_data = {"token": jiwei_token}
        ai_response = KeyDemo.my_request(
            'post',
            url=f"{service_path.service_test_ai}{service_path.service_interfacePath['getAiToken']}",
            headers=headers,
            json=ai_token_data
        )
        ai_token = ai_response["access_token"]
        headers["Authorization"] = f"Bearer {ai_token}"
        return {"headers": [headers]}
    except Exception as e:
        print(f"Error obtaining AI token: {e}")
        return None


if __name__ == '__main__':
    token_headers = ai_token_api()
    if token_headers:
        print("Token headers:", token_headers)
