import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path

# 获取文件相对路径
from testcase.Information.user.get_auth_info import Token

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/setting",
                                                 "test_data_getpreference.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("【GetNewsPreference 获取新闻偏好设置】")
class TestGetpreference:

    @pytest.mark.smoke
    def test_getpreference_smock_api(self):
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get', url=Information_path.Information_pro_url + Information_path.Information_path[
            "preference"], headers=head['headers'][0], params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])

    # @pytest.mark.all
    # @pytest.mark.parametrize("getpreference", data_item[1:])
    # def test_getpreference_api(self, getpreference):
    #     # 打印用例ID和名称到报告中显示
    #     res = KeyDemo.my_request('get',url=Information_path.Information_pro_url + Information_path.Information_path["preference"],headers=head['headers'][0],params=getpreference['parameters'])
    #     exresult_code = getpreference['desired']["status"]
    #     # exresult_errmsg = getpreference['desired']["errmsg"]
    #     assert exresult_code == res["status"], "【GetNewsPreference 获取新闻偏好设置】用例执行失败，返回实际结果是->:%s" % res["status"]
    #     # assert exresult_errmsg == res["errmsg"], "【置GetNewsPreference 获取新闻偏好设置】用例执行失败，返回实际结果是->:%s" % res["errmsg"]
    #


if __name__ == '__main__':
    pytest.main()
