import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals, greater_than
from interface import Information_path, soya_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/trade", "test_trade_list.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("贸易管制")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("controlReport", data_item[0:1])
    def test_control_report(self, controlReport, headers):

        # 贸易管制
        res = KeyDemo.my_request('get',
                                 url=Information_path.Information_eip + Information_path.Information_path[
                                     "tradeList"],
                                 headers=headers,
                                 params=controlReport['parameters'])
        # print(res)
        expesult = data_item[0]['desired']["ret"]
        equals(expesult, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("overseasInstitution", data_item[1:2])
    def test_overseas_institution(self, overseasInstitution, headers):

        # 海外机构
        res = KeyDemo.my_request('get',
                                 url=Information_path.Information_eip + Information_path.Information_path[
                                     "OverseaOrgList"],
                                 headers=headers,
                                 params=overseasInstitution['parameters'])
        # print(res)
        getresult = res["count"]
        equals(getresult, 1)

    @pytest.mark.smoke
    @pytest.mark.parametrize("feed", data_item[2:3])
    def test_trade_feed(self, feed, headers):

        # 管制新闻动态
        res = KeyDemo.my_request('get',
                                 url=Information_path.Information_pro_url + Information_path.Information_path[
                                     "feed"],
                                 headers=headers,
                                 params=feed['parameters'])
        # print(res)
        getresult = res["status"]
        equals(getresult, data_item[0]['desired']["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("controlledList", data_item[3:4])
    def test_controlledList(self, controlledList, headers):

        # 制裁、管制贸易清单
        res = KeyDemo.my_request('get',
                                 url=Information_path.Information_eip + Information_path.Information_path[
                                     "controlledList"],
                                 headers=headers,
                                 params=controlledList['parameters'])
        # print(res)
        getresult = res["count"]
        greater_than(getresult, 1)

    @pytest.mark.smoke
    @pytest.mark.parametrize("controlledInfo", data_item[3:4])
    def test_controlledInfo(self, controlledInfo, headers):

        # 制裁、管制贸易清单详情
        res = KeyDemo.my_request('get',
                                 url=Information_path.Information_eip + Information_path.Information_path[
                                     "controlledInfo"],
                                 headers=headers,
                                 params=controlledInfo['parameters'])
        # print(res)
        getresult = res["count"]
        equals(getresult, data_item[0]['desired']["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("report_statistic", data_item[5:6])
    def test_report_statistic(self, report_statistic, headers):

        # 贸易管制监控
        res = KeyDemo.my_request('get',
                                 url=soya_path.data_utl + soya_path.soya_api_path[
                                     "report_statistic"],
                                 headers=headers,
                                 params=report_statistic['parameters'])
        # print(res)
        getresult = res["data"]["keywords"][0]
        equals(getresult, report_statistic['desired']["keywords"])

if __name__ == '__main__':
    pytest.main()
