import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
from testcase.Information.get_auth_info import token_api
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.user.get_auth_info import Token


data_file_path = ReadJsonFileUtils.get_data_path("data/Information/news", "test_data_feed.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("【资讯流feed】GET请求测试")
class TestFeed:

    @pytest.mark.smoke
    @pytest.mark.parametrize("feed", data_item[0:])
    def test_feed_smock_api(self, feed, headers):
        #
        # head = token_api()

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_pro_url + Information_path.Information_path["feed"],
                                headers=headers,
                                params=feed['parameters'])
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])


if __name__ == '__main__':
    pytest.main()
