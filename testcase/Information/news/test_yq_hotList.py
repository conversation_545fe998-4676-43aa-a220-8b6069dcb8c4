from datetime import date, datetime, timedelta
import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals, time_within_hours
from interface import Information_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.get_auth_info import token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/news", "test_hotList.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


def is_night_time():
    # 获取当前时间
    now = datetime.now()
    # 判断当前时间是否在凌晨0点到3点之间
    return 0 <= now.hour < 3


@allure.feature("【舆情监测】行业热点")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("hotList", data_item[:1])
    def test_yq_data_analysis_time(self, hotList, headers):

        # 打印用例ID和名称到报告中显示
        print("用例name:{}".format(hotList['name']))
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["hotList"],
                                headers=headers,
                                params=hotList['parameters'])

        # 获取预期的analysis_time，并进行格式化处理
        expected_analysis_time = datetime.strptime(res["data"]["list"][0]["analysis_time"], "%Y-%m-%d %H:%M:%S")
        # 获取今天的日期
        current_datetime = datetime.now()

        # 将当前日期和时间格式化为时分秒格式
        actual_date = current_datetime.strftime("%Y-%m-%d %H:%M:%S")

        print(f"================{expected_analysis_time}================")
        print(f"================" + str(actual_date) + "================")
        if is_night_time():
            print("当前是凌晨时间，跳过时间对齐检查")
        else:
            time_within_hours(actual_date, expected_analysis_time, 3, "解析时间差超过 3 小时，请留意集微网舆情")

    @pytest.mark.smoke
    @pytest.mark.parametrize("hotList", data_item[1:2])
    def test_yq_data_public_time(self, hotList, headers):

        # 打印用例ID和名称到报告中显示
        print("用例name:{}".format(hotList['name']))
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["hotList"],
                                headers=headers,
                                params=hotList['parameters'])

        # 获取预期的analysis_time，并进行格式化处理
        expected_public_time = datetime.strptime(res["data"]["list"][0]["public_time"], "%Y-%m-%d %H:%M:%S")

        # 获取今天的日期
        current_datetime = datetime.now()

        # 将当前日期和时间格式化为时分秒格式
        actual_date = current_datetime.strftime("%Y-%m-%d %H:%M:%S")

        print(f"================{expected_public_time}================")
        print(f"================" + str(actual_date) + "================")
        if is_night_time():
            print("当前是凌晨时间，跳过时间对齐检查")
        else:
            time_within_hours(actual_date, expected_public_time, 3, "发布时间差超过 3 小时，请留意集微网舆情")

    @pytest.mark.smoke
    @pytest.mark.parametrize("hotList", data_item[2:])
    def test_yq_hot(self, hotList, headers):
        # 打印用例ID和名称到报告中显示

        print("用例name:{}".format(hotList['name']))
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["hotList"],
                                headers=headers,
                                params=hotList['parameters'])
        # 校验结果码
        expected_result_code = data_item[0]['desired']["result_code"]
        equals(expected_result_code, res["result_code"])


if __name__ == '__main__':
    pytest.main()
