import logging
from datetime import datetime

import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
from testcase.Information.get_auth_info import token_api
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
data_file_path = ReadJsonFileUtils.get_data_path("data/Information/news",
                                                 "test_data_indexCompanyList.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("【首页企业推荐indexCompanyList】")
class TestSearch:
    @pytest.mark.smoke
    @pytest.mark.parametrize("indexCompanyList", data_item[0:])
    def test_indexCompanyList_api(self, indexCompanyList, headers):
        # 现在headers是fixture提供的，不需要在测试方法中重新获取

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "indexCompanyList"],
                                headers=headers,
                                params=indexCompanyList['parameters'])
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])


if __name__ == '__main__':
    pytest.main()
