import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path

# 获取文件相对路径
from testcase.Information.user.get_auth_info import Token

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/news", "test_data_latest.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("News API【PremiumLatest 最新快讯】")
class TestLatest:

    @pytest.mark.smoke
    def test_latest_smock_api(self):
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get',url=Information_path.Information_pro_url + Information_path.Information_path["latest"],headers=head['headers'][0],params=data_item[0]['parameters'])
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])

    @pytest.mark.all
    @pytest.mark.parametrize("latest", data_item[1:])
    def test_latest_api(self, latest):
        # 打印用例ID和名称到报告中显示
        head = Token().token_api()
        res = KeyDemo.my_request('get',url=Information_path.Information_pro_url + Information_path.Information_path["latest"],headers=head['headers'][0],params=latest['parameters'])
        exresult_code = latest['desired']["status"]
        equals(exresult_code, res["status"])


if __name__ == '__main__':
    pytest.main()
