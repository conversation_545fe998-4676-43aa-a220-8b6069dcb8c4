import pytest

from testcase.Information.get_auth_info import token_api, ai_token_api


@pytest.fixture(scope="session")
def headers():
    # 这里调用一次接口获取headers，并作为fixture返回
    return token_api()['headers'][0]


@pytest.fixture(scope="session")
def ai_headers():
    # 这里调用一次接口获取headers，并作为fixture返回
    return ai_token_api()['headers'][0]


@pytest.fixture(scope="session")
def service_headers():
    # 这里调用一次接口获取headers，并作为fixture返回
    return ai_token_api()['headers'][0]


if __name__ == '__main__':
    pytest.main()
