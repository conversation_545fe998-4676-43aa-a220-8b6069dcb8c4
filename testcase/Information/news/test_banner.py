import logging
from datetime import datetime

import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
from testcase.Information.get_auth_info import token_api

# 获取文件相对路径
from testcase.Information.news.get_headers import headers

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/news", "test_banner.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("【首页舆情indexyq】根据热度倒排")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("banner", data_item[0:])
    def test_search_smock_api(self, banner, headers):
        # 打印用例ID和名称到报告中显示 update by sub

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_pro_url + Information_path.Information_path["banner"],
                                headers=headers,
                                params=banner['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["status"]
        equals(exresult_code, res["status"])


if __name__ == '__main__':
    pytest.main()
