from datetime import date, datetime

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.get_auth_info import token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/company", "test_report.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("集微报告")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("report", data_item[0:])
    def test_report(self, report, headers):
        # 集微报告

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_pro_url + Information_path.Information_path[
                                    "jiweiReport"],
                                headers=headers,
                                params=report['parameters'])
        # print(res)
        expesult = data_item[0]['desired']["status"]
        equals(expesult, res["status"])


if __name__ == '__main__':
    pytest.main()
