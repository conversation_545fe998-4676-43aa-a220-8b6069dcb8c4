import time
from datetime import date, datetime

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals, greater_than
from interface import Information_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.get_auth_info import token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/company",
                                                 "test_companyinfo_detail.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')


@allure.feature("【企业库】企业详情")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("companyDetail", data_item[:1])
    # 企业库-企业全览
    def test_company_base_detail(self, companyDetail, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "companyIntro"],
                                headers=headers,
                                params=companyDetail['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("financeReport", data_item[1:2])
    # 企业库-财务报告
    def test_company_financeReport(self, financeReport, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "financeReport"],
                                headers=headers,
                                params=financeReport['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        assert exresult_code == res["ret"], "【企业库-财务报告】用例执行失败，返回实际结果是->:%s" % res["ret"]

    @pytest.mark.smoke
    @pytest.mark.parametrize("companyPolicyInfo", data_item[2:3])
    # 企业库-政策信息
    def test_company_companyPolicyInfo(self, companyPolicyInfo, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "companyPolicyInfo"],
                                headers=headers,
                                params=companyPolicyInfo['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getCompanyInfo", data_item[3:4])
    # 企业库-基础信息
    def test_company_getCompanyInfo(self, getCompanyInfo, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getCompanyInfo"],
                                headers=headers,
                                params=getCompanyInfo['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getOpinion", data_item[4:5])
    # 企业库-企业舆情
    def test_company_getOpinion(self, getOpinion, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["getOpinion"],
                                headers=headers,
                                params=getOpinion['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("zyzblist", data_item[5:6])
    # 企业库-财务数据
    def test_company_getOpinion(self, zyzblist, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["zyzblist"],
                                headers=headers,
                                params=zyzblist['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("plProjectList", data_item[6:7])
    # 企业库-项目信息
    def test_company_getOpinion(self, plProjectList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "plProjectList"],
                                headers=headers,
                                params=plProjectList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getCompanyUser", data_item[7:8])
    # 企业库-企业人员
    def test_company_getOpinion(self, getCompanyUser, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getCompanyUser"],
                                headers=headers,
                                params=getCompanyUser['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getCompanyRecruit", data_item[8:9])
    # 企业库-职场招聘-招聘列表
    def test_company_getCompanyRecruit(self, getCompanyRecruit, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getCompanyRecruit"],
                                headers=headers,
                                params=getCompanyRecruit['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getAntitrustList", data_item[9:10])
    # 企业库-反垄断
    def test_company_getAntitrustList(self, getAntitrustList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getAntitrustList"],
                                headers=headers,
                                params=getAntitrustList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("CustomerList", data_item[10:11])
    # 企业库-合作伙伴
    def test_company_CustomerList(self, CustomerList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "CustomerList"],
                                headers=headers,
                                params=CustomerList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("investmentList", data_item[11:12])
    # 企业库-投资事件
    def test_company_investmentList(self, investmentList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "investmentList"],
                                headers=headers,
                                params=investmentList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getKtAnnouncementList", data_item[12:13])
    # 企业库-开庭公告
    def test_company_getKtAnnouncementList(self, getKtAnnouncementList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getKtAnnouncementList"],
                                headers=headers,
                                params=getKtAnnouncementList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getCourtRegisterList", data_item[13:14])
    # 企业库-立案信息
    def test_company_getCourtRegisterList(self, getCourtRegisterList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getCourtRegisterList"],
                                headers=headers,
                                params=getCourtRegisterList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getLawSuit", data_item[14:15])
    # 企业库-法律诉讼
    def test_company_getLawSuit(self, getLawSuit, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getLawSuit"],
                                headers=headers,
                                params=getLawSuit['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("trademarkList", data_item[15:16])
    # 企业库-商标信息
    def test_company_trademarkList(self, trademarkList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "trademarkList"],
                                headers=headers,
                                params=trademarkList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("patentList", data_item[16:17])
    # 企业库-专利信息
    def test_company_patentList(self, patentList, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "patentList"],
                                headers=headers,
                                params=patentList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("getOpinionPrecise", data_item[17:18])
    # 企业库-舆情政策
    def test_company_getOpinionPrecise(self, getOpinionPrecise, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "getOpinionPrecise"],
                                headers=headers,
                                params=getOpinionPrecise['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("companyDetail", data_item[18:19])
    # 企业库-企业重点事件
    def test_company_base_detail(self, companyDetail, headers):
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "companyIntro"],
                                headers=headers,
                                params=companyDetail['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("question", data_item[19:20])
    # 企业库-AI企业问答
    def t_company_base_detail(self, question, headers):
        get_que_id = KeyDemo.my_request('post',
                                url=Information_path.Information_pro_url + Information_path.Information_path[
                                    "aiQuestion"],
                                headers=headers,
                                json=question['parameters'])

        qu_id = get_que_id["data"]["id"]
        time.sleep(15)
        question['parameters_2']['id'] = qu_id
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_pro_url + Information_path.Information_path[
                                    "aiQuestion"],
                                headers=headers,
                                params=question['parameters_2'])
        act_code=int(res["data"]["list"][0]["process"])
        exresult_code = data_item[0]['desired']["ret"]
        print(f"\033[31m================新提交的id为{qu_id}的问答已经走了{act_code}%。================\033[0m")
        greater_than(act_code, exresult_code)



if __name__ == '__main__':
    pytest.main()
