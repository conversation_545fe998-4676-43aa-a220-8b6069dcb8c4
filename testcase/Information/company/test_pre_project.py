from datetime import date, datetime

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.get_auth_info import token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/company", "test_pre_project.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("优质项目")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("areaRaceList", data_item[0:1])
    def test_areaRaceList(self, areaRaceList, headers):
        # 项目库列表-领域、赛道

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "areaRaceList"],
                                headers=headers,
                                params=areaRaceList['parameters'])
        # print(res)tyx
        expesult = data_item[0]['desired']["result"]
        equals(expesult, res["result_code"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("preProject", data_item[1:2])
    def test_preProject(self, preProject, headers):
        # 项目库列表

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "preProject"],
                                headers=headers,
                                params=preProject['parameters'])
        # print(res)tyx
        expesult = data_item[0]['desired']["result"]
        equals(expesult, res["result_code"])


if __name__ == '__main__':
    pytest.main()
