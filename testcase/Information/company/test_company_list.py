from datetime import date, datetime

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.get_auth_info import token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/company", "test_company_list.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("【企业库】企业列表")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("companyList", data_item[0:])
    def test_company_list(self, companyList, headers):
        # 打印用例ID和名称到报告中显示

        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path["companyList"],
                                headers=headers,
                                params=companyList['parameters'])
        # print(res)
        exresult_code = data_item[0]['desired']["ret"]
        equals(exresult_code, res["ret"])


if __name__ == '__main__':
    pytest.main()
