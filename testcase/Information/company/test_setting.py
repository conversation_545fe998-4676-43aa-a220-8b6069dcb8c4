from datetime import date, datetime

import allure
import pytest

from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import Information_path
# 这个引用不能删，否则关联不到缓存里的header
from testcase.Information.news.get_headers import headers

# 获取文件相对路径
from testcase.Information.get_auth_info import token_api

data_file_path = ReadJsonFileUtils.get_data_path("data/Information/company", "test_settings.json")  # testdatas
# 读取测试数据文件
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')  # 是一个list列表，list列表中包含多个字典


@allure.feature("信息平台设置")
class TestSearch:

    @pytest.mark.smoke
    @pytest.mark.parametrize("yqPlanList", data_item[0:1])
    def test_yqPlanList(self, yqPlanList, headers):
        # 企业监控列表
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "yqPlanList"],
                                headers=headers,
                                params=yqPlanList['parameters'])
        # print(res)
        expesult = data_item[0]['desired']["ret"]
        equals(expesult, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("companyGroupList", data_item[1:2])
    def test_companyGroupList(self, companyGroupList, headers):
        # 企业动态监控分组
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "companyGroupList"],
                                headers=headers,
                                params=companyGroupList['parameters'])
        # print(res)
        expesult = data_item[0]['desired']["ret"]
        equals(expesult, res["ret"])

    @pytest.mark.smoke
    @pytest.mark.parametrize("yqCompanyList", data_item[2:3])
    def test_yqCompanyList(self, yqCompanyList, headers):
        # 企业动态企业列表
        res = KeyDemo.my_request('get',
                                url=Information_path.Information_eip + Information_path.Information_path[
                                    "yqCompanyList"],
                                headers=headers,
                                params=yqCompanyList['parameters'])
        # print(res)
        expesult = data_item[0]['desired']["ret"]
        equals(expesult, res["ret"])


if __name__ == '__main__':
    pytest.main()
