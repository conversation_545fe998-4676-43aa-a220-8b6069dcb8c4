# 自动化测试框架

基于 pytest 的 API 自动化测试框架，支持数据驱动、智能重试和 Allure 报告。

## 目录结构

```
├── interface/          # API 路径定义
├── common/            # 通用工具模块
├── data/              # 测试数据
├── testcase/          # 测试用例
├── config/            # 配置文件
├── tools/             # 工具脚本
├── run.py             # 测试执行入口
└── pytest.ini        # pytest 配置
```

## 新增接口测试

### 1. 定义接口路径
在 `interface/` 中添加新接口：
```python
service_interfacePath = {
    "new_api": "/api/new/endpoint"
}
```

### 2. 准备测试数据
在 `data/` 中创建 JSON 文件：
```json
{
  "testcases": [{
    "name": "测试新接口",
    "parameters": {"param1": "value1"},
    "desired": {"status": 0}
  }]
}
```

### 3. 编写测试用例
在 `testcase/` 中创建测试文件：
```python
@pytest.mark.parametrize("test_data", data_item)
def test_new_api(self, test_data, headers):
    res = KeyDemo.my_request('get', url=url, headers=headers, 
                           params=test_data['parameters'])
    equals(test_data['desired']["status"], res["status"])
```

## 核心特性

- **智能错误报告**：简化错误信息显示
- **自动重试机制**：失败测试自动重试
- **数据驱动测试**：JSON 文件管理测试数据
- **Allure 集成**：生成详细测试报告
- **性能优化**：缓存机制、连接复用、流式处理

## 运行测试

```bash
# 基本运行
python run.py

# 优化运行
python run_optimized.py

# 生成 Allure 报告
pytest --alluredir=allure-results
allure serve allure-results
```
