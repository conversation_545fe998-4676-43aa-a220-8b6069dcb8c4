[pytest]
;addopts = -vs -k "smoke"
addopts = -s --reruns 2 --reruns-delay 2 -p common.pytest_plugins.concise_errors -p common.pytest_plugins.pytest_optimization_plugin -W ignore::DeprecationWarning
python_files = test_*  *_test.py
python_classes = Test*  *Test
python_functions = test*  *test
;rootdir = .
;reruns = 2
;reruns_delay = 1
pythonpath = .

filterwarnings =
    ignore:'urllib3\[secure\]' extra is deprecated:DeprecationWarning


markers =
    smoke : Smoke case
    all: Total case
    login : Login Case
    order : Order Case
    optimize: Mark test for optimization


