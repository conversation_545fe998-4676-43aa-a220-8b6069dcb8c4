{"testcases": [{"name": "新闻水印【给新闻附件增加用户水印】", "id": "produce", "path": "api/watermark/produce", "parameters": {"token": "", "news_id": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "新闻水印【给新闻附件增加用户水印】【token=null】", "id": "produce【token=null】", "path": "api/watermark/produce", "parameters": {"token": "", "news_id": ""}, "desired": {"errno": 600004, "errmsg": "请进入我的页面登录"}}, {"name": "新闻水印【给新闻附件增加用户水印】【token=error】", "id": "produce【token=error】", "path": "api/watermark/produce", "parameters": {"token": "haha", "news_id": ""}, "desired": {"errno": 2000003, "errmsg": "非法的请求"}}, {"name": "新闻水印【给新闻附件增加用户水印】【token=lack】", "id": "produce【token=lack】", "path": "api/watermark/produce", "parameters": {"news_id": ""}, "desired": {"errno": 71, "errmsg": "PARAM 'token' REQUIRED"}}, {"name": "新闻水印【给新闻附件增加用户水印】【news_id=null】", "id": "produce【news_id=null】", "path": "api/watermark/produce", "parameters": {"token": "", "news_id": ""}, "desired": {"errno": 73, "errmsg": "PARAM 'news_id' TYPE ERROR"}}, {"name": "新闻水印【给新闻附件增加用户水印】【news_id=error】", "id": "produce【news_id=null】", "path": "api/watermark/produce", "parameters": {"token": "", "news_id": "00000"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "新闻水印【给新闻附件增加用户水印】【news_id=lack】", "id": "produce【news_id=lack】", "path": "api/watermark/produce", "parameters": {"token": ""}, "desired": {"errno": 71, "errmsg": "PARAM 'news_id' REQUIRED"}}]}