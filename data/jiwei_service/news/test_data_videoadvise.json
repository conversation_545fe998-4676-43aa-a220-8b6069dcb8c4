{"testcases": [{"name": "资讯详情相关接口【资讯改版-视频推荐】", "id": "videoadvise", "path": "api/news/videoadvise", "parameters": {"source": "pc"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-视频推荐】【source=ios】", "id": "videoadvise【source=ios】", "path": "api/news/videoadvise", "parameters": {"source": "ios"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-视频推荐】【source=Android】", "id": "videoadvise【source=Android】", "path": "api/news/videoadvise", "parameters": {"source": "Android"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-视频推荐】【source=null】", "id": "videoadvise【source=null】", "path": "api/news/videoadvise", "parameters": {"source": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-视频推荐】【source=error】", "id": "videoadvise【source=error】", "path": "api/news/videoadvise", "parameters": {"source": "哈哈"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-视频推荐】【source=lack】", "id": "videoadvise【source=lack】", "path": "api/news/videoadvise", "parameters": {}, "desired": {"errno": 71, "errmsg": "PARAM 'source' REQUIRED"}}]}