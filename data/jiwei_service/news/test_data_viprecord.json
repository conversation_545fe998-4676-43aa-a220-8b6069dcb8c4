{"testcases": [{"name": "资讯详情相关接口【资讯改版-VIP浏览记录】", "id": "viprecord", "path": "/api/news/viprecord", "parameters": {"source": "pc", "token": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【source=ios】", "id": "viprecord【source=ios】", "path": "/api/news/viprecord", "parameters": {"source": "ios", "token": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【source=Android】", "id": "viprecord【source=Android】", "path": "/api/news/viprecord", "parameters": {"source": "Android", "token": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【source=null】", "id": "viprecord【source=null】", "path": "/api/news/viprecord", "parameters": {"source": "", "token": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【source=error】", "id": "viprecord【source=error】", "path": "/api/news/viprecord", "parameters": {"source": "哈哈", "token": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【source=lack】", "id": "viprecord【source=lack】", "path": "/api/news/viprecord", "parameters": {"token": ""}, "desired": {"errno": 71, "errmsg": "PARAM 'source' REQUIRED"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【token=null】", "id": "viprecord【token=null】", "path": "/api/news/viprecord", "parameters": {"source": "", "token": ""}, "desired": {"errno": 600004, "errmsg": "请进入我的页面登录"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【token=error】", "id": "viprecord【token=error】", "path": "/api/news/viprecord", "parameters": {"source": "", "token": "哈哈"}, "desired": {"errno": 2000003, "errmsg": "非法的请求"}}, {"name": "资讯详情相关接口【资讯改版-VIP浏览记录】【token=lack】", "id": "viprecord【token=lack】", "path": "/api/news/viprecord", "parameters": {"source": ""}, "desired": {"errno": 71, "errmsg": "PARAM 'token' REQUIRED"}}]}