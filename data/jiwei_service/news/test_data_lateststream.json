{"testcases": [{"name": "资讯详情相关接口【获取最新资讯】", "id": "lateststream", "path": "/api/news/lateststream", "parameters": {"source": "pc"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取最新资讯】【source=ios】", "id": "lateststream【source=ios】", "path": "/api/news/lateststream", "parameters": {"source": "ios"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取最新资讯】【source=Android】", "id": "lateststream【source=Android】", "path": "/api/news/lateststream", "parameters": {"source": "Android"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取最新资讯】【source=null】", "id": "lateststream【source=null】", "path": "/api/news/lateststream", "parameters": {"source": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取最新资讯】【source=error】", "id": "lateststream【source=error】", "path": "/api/news/lateststream", "parameters": {"source": "哈哈"}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取最新资讯】【source=lack】", "id": "lateststream【source=lack】", "path": "/api/news/lateststream", "parameters": {}, "desired": {"errno": 71, "errmsg": "PARAM 'source' REQUIRED"}}]}