{"testcases": [{"name": "资讯详情相关接口【获取作者资讯】", "id": "authorrecent", "path": "/api/news/authorrecent", "parameters": {"source": "pc", "author_id": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取作者资讯】【source=ios】", "id": "authorrecent【source=ios】", "path": "/api/news/authorrecent", "parameters": {"source": "ios", "author_id": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取作者资讯】【source=Android】", "id": "authorrecent【source=Android】", "path": "/api/news/authorrecent", "parameters": {"source": "Android", "author_id": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取作者资讯】【source=null】", "id": "authorrecent【source=null】", "path": "/api/news/authorrecent", "parameters": {"source": "", "author_id": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取作者资讯】【source=error】", "id": "authorrecent【source=error】", "path": "/api/news/authorrecent", "parameters": {"source": "哈哈", "author_id": ""}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}, {"name": "资讯详情相关接口【获取作者资讯】【source=lack】", "id": "authorrecent【source=lack】", "path": "/api/news/authorrecent", "parameters": {"author_id": ""}, "desired": {"errno": 71, "errmsg": "PARAM 'source' REQUIRED"}}, {"name": "资讯详情相关接口【获取作者资讯】【author_id=null】", "id": "authorrecent【author_id=null】", "path": "/api/news/authorrecent", "parameters": {"source": "pc", "author_id": 0}, "desired": {"errno": 0, "errmsg": "SUCCESS"}}]}