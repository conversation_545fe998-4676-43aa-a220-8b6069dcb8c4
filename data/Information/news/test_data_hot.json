{"testcases": [{"name": "News API【PremiumHot 热门文章】", "id": "hot", "path": "/api/news/hot", "parameters": {"limit": 10, "data": 30, "category_ids": 406}, "desired": {"status": 0}}, {"name": "News API【PremiumHot 热门文章】【data=超出取值范围】", "id": "hot【data=超出取值范围】", "path": "/api/news/hot", "parameters": {"limit": 10, "data": 367, "category_ids": 406}, "desired": {"status": 0}}, {"name": "News API【PremiumHot 热门文章】【category_ids=错误】", "id": "hot【category_ids=错误】", "path": "/api/news/hot", "parameters": {"limit": 10, "data": 367, "category_ids": 999999}, "desired": {"status": 0}}]}