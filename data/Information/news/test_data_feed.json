{"testcases": [{"name": "首页资讯-行业报告推荐(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 3, "category_ids": "436,438,238,443,444", "cat_only": 1, "tags": ""}, "desired": {"status": 0}}, {"name": "首页资讯-政策指引推荐(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 4, "category_ids": "439,440,441", "cat_only": 1, "tags": ""}, "desired": {"status": 0}}, {"name": "行业信息-推荐(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 10, "category_ids": "406,435,259", "cat_only": 1, "tags": ""}, "desired": {"status": 0}}, {"name": "政策指引-推荐(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 10, "category_ids": "439,440,441", "cat_only": 1, "tags": ""}, "desired": {"status": 0}}, {"name": "集微咨询-并购报告(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 10, "category_ids": "438", "cat_only": 1, "tags": ""}, "desired": {"status": 0}}, {"name": "搜索-行业信息(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 10, "category_ids": "406,435,259", "cat_only": 0, "keyword": "智驾", "tags": ""}, "desired": {"status": 0}}, {"name": "搜索-政策指引(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 10, "category_ids": "439,440,441", "cat_only": 0, "keyword": "芯片", "tags": ""}, "desired": {"status": 0}}, {"name": "搜索-集微咨询(冒烟测试用例)", "id": "feed【冒烟测试用例】", "path": "/api/news/feed", "parameters": {"limit": 10, "category_ids": "436,438,238,443,444", "cat_only": 0, "keyword": "芯片", "tags": ""}, "desired": {"status": 0}}]}