#!/usr/bin/env python
# _*_ coding:utf-8 _*_
import os, sys

sys.path.append(os.path.dirname(__file__))
import configparser
from common.request import KeyDemo
from common import setting
from interface import oms_path, emp_path, service_path, mp_path, ACP_ADMIN_path, ACP_checkin_path


def getAuthorization(system_, account):
    system_ = system_.lower()
    conf = configparser.ConfigParser()
    conf.read(setting.TEST_CONFIG, encoding='UTF-8')
    if system_ == 'oms':
        url = oms_path.oms_test_url + oms_path.oms_login["login"]
        return login(url, account)

    if system_ == 'emp1':
        url = emp_path.emp_test_url + emp_path.emp_path["login"]
        return login(url, account)
    # 暂时用不到，目前登录接口返回中抓取不到token_type字段了
    if system_ == 'service':
        url = service_path.service_test_url + service_path.service_interfacePath["login"]
        return login(url, account)

    if system_ == 'acp_admin':
        url = ACP_ADMIN_path.ACP_ADMIN_test_url + ACP_ADMIN_path.ACP_ADMIN_interfacePath["login"]
        return login(url, account)
    if system_ == 'acp_checkin':
        url = ACP_checkin_path.ACP_checkin_login_url + ACP_checkin_path.ACP_checkin_login['login']
        return login(url, account)

    if system_ == 'mp':
        url = mp_path.base_url + mp_path.mp_login["login"]
        return login(url, account)


def login(url, account):
    # 授权登录token
    try:

        if url.find("baseurl_short") != -1:
            res = KeyDemo.my_request('post', url, account)
            token = res["data"]["token"]
            return token
        else:
            token = {'Authorization': 'token'}
            res = KeyDemo.my_request('post', url, account)
            authorization_token = res["token"]
            authorization_token_type = res["token_type"]
            authorization = authorization_token_type + " " + authorization_token
            token['Authorization'] = authorization
            return token
    except:
        print('authorization获取异常')
        sys.exit()


def getToken(system_, account):
    system_ = system_.lower()
    conf = configparser.ConfigParser()
    conf.read(setting.TEST_CONFIG, encoding='UTF-8')
    if system_ == 'oms':
        url = oms_path.oms_test_url + oms_path.oms_interfacePath["login"]
        return login(url, account)

    elif system_ == 'emp1':
        url = emp_path.emp_test_url + emp_path.emp_interfacePath["login"]
    # 授权登录token
    try:
        res = KeyDemo.my_request('post', url, account)
        token = res["token"]
        return token
    except:
        print('token获取异常')
        sys.exit()


def getServiceToken(system_, account):
    system_ = system_.lower()
    conf = configparser.ConfigParser()
    conf.read(setting.TEST_CONFIG, encoding='UTF-8')
    if system_ == 'service':
        url = service_path.service_test_url + service_path.service_interfacePath["login"]
    # 授权登录token
    try:
        res = KeyDemo.my_request('post', url=url, data=account)
        token = res["data"]["token"]
        return token
    except:
        print('token获取异常')
        sys.exit()


def getServiceAuthorization(system_, account):
    system_ = system_.lower()
    conf = configparser.ConfigParser()
    conf.read(setting.TEST_CONFIG, encoding='UTF-8')
    if system_ == 'service':
        url = service_path.service_test_url + service_path.service_interfacePath["login"]
    try:
        token = {'Authorization': 'token'}
        res = KeyDemo.my_request('post', url=url, data=account)
        authorization_token = res["data"]["token"]
        authorization_token_type = "Bearer"
        authorization = authorization_token_type + " " + authorization_token
        token['Authorization'] = authorization
        return token
    except:
        print('authorization获取异常')
        sys.exit()
