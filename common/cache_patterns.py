"""
缓存模式定义，用于优化API调用。
本模块定义了不同类型API调用的缓存模式。
"""

import re
from typing import Dict, List, Pattern, Any, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义不同缓存策略的URL模式
# 格式: (正则表达式模式, 缓存秒数, 是否缓存POST请求)
URL_CACHE_PATTERNS = [
    # 认证端点 - 长缓存(1小时)
    (r'.*/(login|token|auth|authorization|pwd/login|access/token|oauth/login).*', 3600, True),

    # 用户信息 - 中等缓存(10分钟)
    (r'.*/user/base.*', 600, True),

    # 报告端点 - 中等缓存(10分钟)
    (r'.*/report/search.*', 600, True),
    (r'.*/report/.*', 600, True),

    # 新闻feed - 短缓存(5分钟)
    (r'.*/news/feed.*', 300, True),
    (r'.*/news/hot.*', 300, True),
    (r'.*/news/latest.*', 300, True),

    # 交易信息 - 中等缓存(10分钟)
    (r'.*/trade/list.*', 600, True),

    # 观点端点 - 短缓存(5分钟)
    (r'.*/opinion/hotList.*', 300, True),

    # 作者平台 - 中等缓存(10分钟)
    (r'.*/v2/article.*', 600, True),
    (r'.*/v1/article.*', 600, True),
    (r'.*/article.*', 600, True),

    # 新闻内容端点 - 短缓存(5分钟)
    (r'.*/news/feedstream.*', 300, True),
    (r'.*/special/audiolist.*', 300, True),
    (r'.*/news/newsdetail.*', 300, True),
    (r'.*/news/vipfeed.*', 300, True),
    (r'.*/api/aigc/conversation.*', 600, True),

    # 其他GET请求的默认模式 - 短缓存(2分钟)
    (r'.*', 120, False)
]

# 编译正则模式以加速匹配
COMPILED_PATTERNS = [(re.compile(pattern), duration, cache_post)
                     for pattern, duration, cache_post in URL_CACHE_PATTERNS]


def get_cache_config(url: str, method: str) -> Tuple[int, bool]:
    """
    获取URL的缓存配置

    参数:
        url: 要检查的URL
        method: HTTP方法(GET, POST等)

    返回:
        (缓存秒数, 是否缓存POST请求)元组
    """
    method_lower = method.lower()

    # Find the first matching pattern
    for pattern, duration, cache_post in COMPILED_PATTERNS:
        if pattern.match(url):
            # For POST requests, only cache if explicitly allowed
            if method_lower == 'post' and not cache_post:
                return 0, False
            return duration, cache_post

    # Default: don't cache
    return 0, False


def should_ignore_params(url: str) -> bool:
    """
    确定此URL的缓存是否应忽略查询参数。
    某些端点无论参数如何都返回相同数据。

    参数:
        url: 要检查的URL

    返回:
        如果应忽略参数则为True，否则为False
    """
    # List of URL patterns where parameters should be ignored
    ignore_params_patterns = [
        r'.*/news/feed.*',
        r'.*/report/search.*',
        r'.*/user/base.*',
        r'.*/opinion/hotList.*',
        r'.*/trade/list.*',
        r'.*/v2/article.*',
        r'.*/api/aigc/conversation.*',
    ]

    # Check if URL matches any pattern
    for pattern in ignore_params_patterns:
        if re.match(pattern, url):
            return True

    return False


def should_cache_request(url: str, method: str, **kwargs) -> bool:
    """
    根据URL和方法确定请求是否应被缓存

    参数:
        url: 要检查的URL
        method: HTTP方法(GET, POST等)
        **kwargs: 额外请求参数

    返回:
        如果请求应被缓存则为True，否则为False
    """
    method_lower = method.lower()
    duration, cache_post = get_cache_config(url, method_lower)

    # Always cache GET requests if there's a duration
    if method_lower == 'get' and duration > 0:
        return True

    # For POST requests, only cache if explicitly allowed
    if method_lower == 'post' and cache_post and duration > 0:
        return True

    return False


def get_cache_duration(url: str, method: str) -> int:
    """
    获取URL的缓存持续时间

    参数:
        url: 要检查的URL
        method: HTTP方法(GET, POST等)

    返回:
        缓存秒数
    """
    duration, _ = get_cache_config(url, method)
    return duration


def is_cacheable_post(url: str) -> bool:
    """
    检查此URL的POST请求是否应被缓存

    参数:
        url: 要检查的URL

    返回:
        如果此URL的POST请求应被缓存则为True
    """
    _, cache_post = get_cache_config(url, 'post')
    return cache_post
