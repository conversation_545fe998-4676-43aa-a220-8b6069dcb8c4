"""
原始request模块的兼容层。
本模块内部使用优化的HTTP工具，
但保持相同接口以实现向后兼容。
"""

import json
import logging
from typing import Dict, Any, Optional, Union, List

# 导入优化的HTTP工具
from common.http_utils import HttpClient, get_text as optimized_get_text

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_text(res, key):
    """
    使用JSONPath从JSON响应中提取值。
    与原始get_text函数兼容。

    参数:
        res: JSON响应(字符串或字典)
        key: 要提取的键

    返回:
        提取的值，如果未找到则返回None
    """
    logger.debug(f"Getting text for key: {key}")
    return optimized_get_text(res, key)


class KeyDemo:
    """
    具有会话重用、缓存和错误处理的HTTP客户端。
    与原始KeyDemo接口兼容。
    """

    # 使用示例
    # headers_fixture = get_headers()  # 假设这是你的headers fixture
    # response = my_request('GET', 'http://example.com', headers=headers_fixture)
    # POST请求JSON数据: json=json_data
    # POST请求表单数据: data=form_data
    # GET请求查询参数: params=query_params

    @staticmethod
    def my_request(method, url, **kwargs):
        """
        发起HTTP请求，具有会话重用、缓存和错误处理功能。
        与原始KeyDemo.my_request方法兼容。

        参数:
            method: HTTP方法
            url: 请求URL
            **kwargs: 额外请求参数

        返回:
            解析后的JSON响应(字典)，如果请求失败则返回None
        """
        logger.debug(f"Making request: {method} {url}")
        return HttpClient.my_request(method, url, **kwargs)
