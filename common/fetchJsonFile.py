"""
原始getfile函数的兼容层。
本模块内部使用优化的JsonUtils类，
但保持相同接口以实现向后兼容。
"""

import os
import sys
import json
import logging
from typing import Dict, Any

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
sys.path.append(BASE_DIR)
from common import setting
from common.json_utils import get_json_file, JsonUtils

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def getfile(system, fileName):
    """
    从特定系统文件夹加载JSON数据。
    这是一个向后兼容的函数，内部使用优化的JSON工具。

    参数:
        system: 系统文件夹名称
        fileName: JSON文件名

    返回:
        解析后的JSON数据字典
    """
    logger.debug(f"Getting file from system: {system}, file: {fileName}")
    return get_json_file(system, fileName)
