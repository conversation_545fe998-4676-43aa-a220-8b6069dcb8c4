"""
优化的HTTP请求工具，具有会话重用、缓存和更好的错误处理功能。
"""

import json
import time
import logging
import hashlib
import threading
from typing import Dict, Any, Optional, Union, List, Tuple
from urllib.parse import urlparse, parse_qs, urlencode

import requests
import jsonpath
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

# 导入认证管理器
from common.auth_manager import (
    get_token, save_token, is_auth_request, is_auth_url,
    clear_token_cache, set_token_max_age
)

# 导入缓存模式
from common.cache_patterns import (
    should_cache_request, get_cache_duration, is_cacheable_post
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局会话和缓存，带锁保证线程安全
_http_sessions: Dict[str, requests.Session] = {}
_response_cache: Dict[str, Any] = {}
_response_cache_timestamp: Dict[str, float] = {}
_response_cache_duration: Dict[str, int] = {}  # Store duration for each cached item
_cache_lock = threading.RLock()
_default_cache_max_age = 60  # Default 1 minute cache validity


def normalize_url(url: str) -> str:
    """
    规范化URL，移除查询参数和片段。

    参数:
        url: 要规范化的URL

    返回:
        规范化后的URL
    """
    parsed = urlparse(url)
    return f"{parsed.scheme}://{parsed.netloc}{parsed.path}"


def get_cache_key(method: str, url: str, **kwargs) -> str:
    """
    为HTTP请求生成缓存键。

    参数:
        method: HTTP方法
        url: 请求URL
        **kwargs: 额外请求参数

    返回:
        表示请求的哈希字符串
    """
    # 在这里导入以避免循环导入
    from common.cache_patterns import should_ignore_params

    # Parse the URL to extract query parameters
    parsed_url = urlparse(url)

    # Check if we should ignore parameters for this URL
    ignore_params = should_ignore_params(url)

    # Only include certain kwargs in the cache key
    cache_kwargs = {}

    if not ignore_params:
        # Extract query parameters from URL
        url_params = {}
        if parsed_url.query:
            for k, v in parse_qs(parsed_url.query).items():
                url_params[k] = v[0] if len(v) == 1 else v

        # Handle query parameters from kwargs
        if 'params' in kwargs and kwargs['params']:
            # Sort params for consistent key generation
            if isinstance(kwargs['params'], dict):
                params_dict = {k: kwargs['params'][k] for k in sorted(kwargs['params'].keys())}
                # Merge with URL params
                params_dict.update(url_params)
                cache_kwargs['params'] = params_dict
            else:
                cache_kwargs['params'] = kwargs['params']
        elif url_params:
            # If no params in kwargs but URL has query params
            cache_kwargs['params'] = url_params

        # Handle JSON body
        if 'json' in kwargs and kwargs['json']:
            # For JSON data, we need to ensure consistent serialization
            if isinstance(kwargs['json'], dict):
                # Sort keys for consistent serialization
                cache_kwargs['json'] = json.dumps(kwargs['json'], sort_keys=True)
            else:
                cache_kwargs['json'] = json.dumps(kwargs['json'])

        # Handle form data
        if 'data' in kwargs and kwargs['data']:
            if isinstance(kwargs['data'], dict):
                cache_kwargs['data'] = {k: kwargs['data'][k] for k in sorted(kwargs['data'].keys())}
            else:
                cache_kwargs['data'] = kwargs['data']

        # Handle headers that might affect caching
        if 'headers' in kwargs and kwargs['headers']:
            # Only include certain headers that might affect the response
            cache_headers = {}
            if isinstance(kwargs['headers'], dict):
                for header in ['Accept', 'Content-Type']:
                    if header in kwargs['headers']:
                        cache_headers[header] = kwargs['headers'][header]

                if cache_headers:
                    cache_kwargs['headers'] = cache_headers

    # Create a string representation of the request
    # Use normalized URL to avoid issues with trailing slashes, etc.
    normalized_url = normalize_url(url)

    # For endpoints where we ignore parameters, just use the normalized URL
    if ignore_params:
        request_str = f"{method.lower()}:{normalized_url}"
    else:
        request_str = f"{method.lower()}:{normalized_url}:{json.dumps(cache_kwargs, sort_keys=True)}"

    # Generate a hash of the request string
    hash_key = hashlib.md5(request_str.encode()).hexdigest()

    return hash_key


def is_cache_valid(cache_key: str) -> bool:
    """
    检查请求的缓存响应是否仍然有效。

    参数:
        cache_key: 请求的缓存键

    返回:
        如果缓存有效则为True，否则为False
    """
    with _cache_lock:
        # Check if request exists in cache
        if cache_key not in _response_cache:
            return False

        # Check if cache is too old
        cache_time = _response_cache_timestamp.get(cache_key, 0)
        cache_duration = _response_cache_duration.get(cache_key, _default_cache_max_age)

        if (time.time() - cache_time) > cache_duration:
            # Cache expired, remove it
            del _response_cache[cache_key]
            del _response_cache_timestamp[cache_key]
            del _response_cache_duration[cache_key]
            return False

        return True


def get_session(base_url: str) -> requests.Session:
    """
    获取或创建具有连接池和重试逻辑的基础URL会话。

    参数:
        base_url: 会话的基础URL

    返回:
        requests.Session对象
    """
    # Extract the domain from the URL to use as the session key
    domain = urlparse(base_url).netloc

    # Return existing session if available
    if domain in _http_sessions:
        return _http_sessions[domain]

    # Create a new session with connection pooling
    session = requests.Session()

    # Configure retry logic
    retry_strategy = Retry(
        total=3,  # Maximum number of retries
        backoff_factor=0.5,  # Backoff factor for retries
        status_forcelist=[429, 500, 502, 503, 504],  # Status codes to retry on
        allowed_methods=["GET", "POST", "PUT", "DELETE", "PATCH"]  # Methods to retry
    )

    # Mount the retry adapter to the session
    adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=10)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Store the session for reuse
    _http_sessions[domain] = session

    return session


def make_request(method: str, url: str, use_cache: bool = True, **kwargs) -> Union[Dict[str, Any], str, None]:
    """
    发起HTTP请求，具有会话重用、缓存和错误处理功能。

    参数:
        method: HTTP方法
        url: 请求URL
        use_cache: 是否使用缓存(默认: True)
        **kwargs: 额外请求参数

    返回:
        解析后的JSON响应(字典)，或原始文本响应(字符串)，如果请求失败则返回None
    """
    method_lower = str(method).lower()

    # Check if this is an authentication request
    is_auth = is_auth_request(method_lower, url, **kwargs)

    # For authentication requests, try to use cached token
    if is_auth and use_cache:
        # Extract credentials from kwargs
        credentials = {}
        if 'params' in kwargs:
            credentials.update(kwargs['params'])
        if 'json' in kwargs:
            credentials.update(kwargs['json'])
        if 'data' in kwargs:
            credentials.update(kwargs['data'])

        # Try to get token from cache
        cached_token = get_token(url, credentials)
        if cached_token:
            logger.debug(f"Cache hit for auth token: {url}")
            return cached_token

    # Determine if we can cache this request based on URL patterns
    can_cache = use_cache and should_cache_request(url, method_lower, **kwargs)

    # Try to get from cache if cacheable
    if can_cache:
        cache_key = get_cache_key(method_lower, url, **kwargs)

        # Return cached response if valid
        if is_cache_valid(cache_key):
            logger.debug(f"Cache hit for {method_lower} {url}")

            with _cache_lock:
                return _response_cache[cache_key]

    # Get or create a session for the URL
    base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
    session = get_session(base_url)

    try:
        # Make the request
        response = session.request(method_lower, url, **kwargs)

        # Raise an exception for bad status codes
        response.raise_for_status()

        # Attempt to parse the response as JSON
        try:
            json_response = response.json()

            # If JSON parsing is successful, proceed with caching and returning JSON
            if can_cache:
                cache_key = get_cache_key(method_lower, url, **kwargs)
                cache_duration = get_cache_duration(url, method_lower)

                with _cache_lock:
                    _response_cache[cache_key] = json_response
                    _response_cache_timestamp[cache_key] = time.time()
                    _response_cache_duration[cache_key] = cache_duration
                logger.debug(f"Cached response for {method_lower} {url} for {cache_duration} seconds")

            if is_auth and use_cache:
                credentials = {}
                if 'params' in kwargs:
                    credentials.update(kwargs['params'])
                if 'json' in kwargs:
                    credentials.update(kwargs['json'])
                if 'data' in kwargs:
                    credentials.update(kwargs['data'])
                save_token(url, credentials, json_response)
                logger.debug(f"Cached auth token for {url}")

            return json_response

        except requests.exceptions.JSONDecodeError as e:
            # If JSON parsing fails, log the error and return the raw response text.
            # Do not cache this, as the cache is intended for parsed JSON objects.
            logger.warning(f"Failed to parse JSON response from {url}: {e}. Returning raw text content.")
            return response.text

    except requests.exceptions.HTTPError as e:
        # Log HTTP errors (4xx, 5xx) and include response text if available
        response_text = e.response.text if e.response else "N/A"
        logger.error(f"HTTP error for {url}: {e} - Response: {response_text}")
        return None

    except requests.exceptions.ConnectionError as e:
        logger.error(f"Connection error for {url}: {e}")
        return None

    except requests.exceptions.Timeout as e:
        logger.error(f"Timeout error for {url}: {e}")
        return None

    except requests.exceptions.RequestException as e: # Catch other request-related exceptions
        logger.error(f"Request error for {url}: {e}")
        return None

    except Exception as e:
        logger.error(f"Unexpected error for {url}: {e}")
        return None


def extract_json_value(data: Dict[str, Any], key: str) -> Any:
    """
    使用JSONPath从JSON数据中提取值。

    参数:
        data: JSON数据(字典)
        key: JSONPath表达式

    返回:
        提取的值，如果未找到则返回None
    """
    if data is None:
        return None

    try:
        # Use JSONPath to extract the value
        value = jsonpath.jsonpath(data, f'$..{key}')

        # Return the value if found
        if value:
            return value[0] if len(value) == 1 else value

        return None

    except Exception as e:
        logger.error(f"Error extracting JSON value for key {key}: {e}")
        return None


def clear_cache() -> None:
    """清除所有缓存（响应和令牌）以释放内存。"""
    with _cache_lock:
        global _response_cache, _response_cache_timestamp, _response_cache_duration
        _response_cache.clear()
        _response_cache_timestamp.clear()
        _response_cache_duration.clear()

    clear_token_cache()
    logger.info("HTTP caches cleared")


def set_cache_max_age(seconds: int, token_seconds: Optional[int] = None) -> None:
    """
    设置缓存响应和令牌的最大有效期。

    参数:
        seconds: 响应缓存的最大有效期（秒）
        token_seconds: 令牌缓存的最大有效期（秒）（默认：与响应缓存相同）
    """
    global _default_cache_max_age

    with _cache_lock:
        _default_cache_max_age = seconds

    # Set token cache max age if provided, otherwise use the same value
    if token_seconds is None:
        token_seconds = seconds

    # Set token cache max age
    set_token_max_age(token_seconds)

    logger.info(f"Default HTTP response cache max age set to {seconds} seconds")
    logger.info(f"Token cache max age set to {token_seconds} seconds")


def close_all_sessions() -> None:
    """Close all HTTP sessions."""
    global _http_sessions
    for domain, session in _http_sessions.items():
        session.close()
    _http_sessions.clear()
    logger.info("All HTTP sessions closed")


class HttpClient:
    """
    HTTP client with session reuse, caching, and error handling.
    Compatible with the original KeyDemo interface.
    """

    @staticmethod
    def my_request(method: str, url: str, **kwargs) -> Union[Dict[str, Any], str, None]:
        """
        发起HTTP请求，具有会话重用、缓存和错误处理功能。
        与原始KeyDemo.my_request方法兼容。

        参数:
            method: HTTP方法
            url: 请求URL
            **kwargs: 额外请求参数

        返回:
            解析后的JSON响应(字典)，或原始文本响应(字符串)，如果请求失败则返回None
        """
        return make_request(method, url, **kwargs)


# Backwards compatibility function
def get_text(res, key):
    """
    使用JSONPath从JSON响应中提取值。
    与原始get_text函数兼容。

    参数:
        res: JSON响应(字符串或字典)
        key: 要提取的键

    返回:
        提取的值，如果未找到则返回None
    """
    if res is None:
        return None

    try:
        # Convert string to JSON if needed
        data = json.loads(res) if isinstance(res, str) else res

        # Use the extract_json_value function
        return extract_json_value(data, key)

    except Exception as e:
        logger.error(f"Error in get_text for key {key}: {e}")
        return None
