# encoding='utf-8'
"""
原始ReadJsonFileUtils类的兼容层。
本模块内部使用优化的JsonFileReader类，
但保持相同接口以实现向后兼容。
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union, List

# 导入优化的JSON工具
from common.json_utils import JsonFileReader, JsonUtils

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ReadJsonFileUtils(JsonFileReader):
    """
    向后兼容的类，继承自优化的JsonFileReader。
    这确保现有代码无需更改即可继续工作。
    """

    def __init__(self, file_name):
        """
        使用文件名初始化。

        参数:
            file_name: JSON文件的路径
        """
        super().__init__(file_name)
        logger.debug(f"ReadJsonFileUtils initialized with file: {file_name}")

    def get_data(self):
        """
        从文件加载JSON数据。

        返回:
            解析后的JSON数据（字典）
        """
        logger.debug(f"Loading data from file: {self.file_name}")
        return super().get_data()

    def get_value(self, id):
        """
        通过键从JSON数据获取值。

        参数:
            id: 从JSON数据中检索的键

        返回:
            与键关联的值
        """
        logger.debug(f"Getting value for key: {id}")
        return super().get_value(id)

    @staticmethod
    def get_data_path(folder, fileName):
        """
        获取JSON数据文件的绝对路径。

        参数:
            folder: 相对于项目根目录的文件夹路径
            file_name: JSON文件名

        返回:
            JSON文件的绝对路径
        """
        logger.debug(f"Getting data path for folder: {folder}, file: {fileName}")
        return JsonUtils.get_data_path(folder, fileName)


# For testing purposes
if __name__ == '__main__':
    data_file_path = ReadJsonFileUtils.get_data_path("data/author_platform/article_management", "article_list.json")
    # 读取文件中的dataItem,是一个list列表，list列表中包含多个字典
    param_data = ReadJsonFileUtils(data_file_path)
    data_item = param_data.get_value('testcases')
    print(data_item)
