import pytest
import re
import os
import sys
import warnings
from _pytest.runner import runtestprotocol

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# Filter out the urllib3 deprecation warning
warnings.filterwarnings("ignore", message="'urllib3\\[secure\\]' extra is deprecated")


def extract_api_info(longrepr_str):
    """
    从错误信息中提取 API 相关信息。

    该方法通过正则表达式从测试错误信息中提取 API URL 和名称，帮助定位问题发生在哪个 API 调用上。

    参数:
        longrepr_str (str): 测试失败的完整错误信息字符串

    返回:
        str: 提取出的 API 名称，如果无法提取则返回 None
    """
    # 尝试查找 API URL
    url_match = re.search(r'url=([^,\s]+)', longrepr_str)
    api_url = url_match.group(1) if url_match else None

    # 如果找到 URL，则从中提取 API 名称
    api_name = None
    if api_url:
        # 尝试提取 URL 路径的最后一部分，通常是 API 名称
        path_match = re.search(r'/([^/]+)(?:\?|$)', api_url)
        if path_match:
            api_name = path_match.group(1)
        else:
            # 如果无法从 URL 中提取，则使用整个 URL 作为 API 名称
            api_name = api_url

    return api_name


def extract_test_name(longrepr_str):
    """
    从错误信息中提取测试用例名称。

    该方法通过正则表达式从错误信息中识别并提取测试函数名称，支持两种常见的测试命名模式：
    1. 独立的测试函数 (test_*)
    2. 测试类中的测试方法 (TestClass.test_*)

    参数:
        longrepr_str (str): 测试失败的完整错误信息字符串

    返回:
        str: 提取出的测试名称，如果无法提取则返回 "Unknown test"
    """
    # 查找 test_* 模式，这通常是测试函数名称
    test_name_match = re.search(r'test_(\w+)', longrepr_str)
    if test_name_match:
        return test_name_match.group(0)

    # 查找 TestClass.test_* 模式，这是测试类中的测试方法
    class_test_match = re.search(r'(\w+)\.test_(\w+)', longrepr_str)
    if class_test_match:
        return f"{class_test_match.group(1)}.{class_test_match.group(2)}"

    # 如果无法识别测试名称，返回默认值
    return "Unknown test"


def extract_error_message(longrepr_str):
    """
    从错误信息中提取实际的错误消息。

    该方法尝试从完整的错误信息中提取最有用的错误消息部分，按以下优先级：
    1. 断言错误信息
    2. 时间差解析错误
    3. 最后一行非空内容（作为备选）

    参数:
        longrepr_str (str): 测试失败的完整错误信息字符串

    返回:
        str: 提取出的错误消息，如果无法提取则返回 "Unknown error"
    """
    # 尝试查找断言错误信息
    assertion_match = re.search(r'AssertionError: (.+?)(?:\n|$)', longrepr_str)
    if assertion_match:
        return assertion_match.group(1).strip()

    # 查找时间差解析特定错误
    time_error_match = re.search(r'解析时间差超过 \d+ 小时，请留意.+', longrepr_str)
    if time_error_match:
        return time_error_match.group(0)

    # 如果没有找到特定错误，获取最后一个非空行
    lines = [line.strip() for line in longrepr_str.split('\n') if line.strip()]
    if lines:
        return lines[-1]

    # 如果无法提取任何有用信息，返回默认错误消息
    return "Unknown error"


def get_concise_error_message(report):
    """
    从测试报告中提取简洁的错误消息。

    该方法整合了测试名称、API 信息和错误消息，生成一个格式化的简洁错误摘要，
    使测试失败信息更易于阅读和理解。

    参数:
        report (pytest.TestReport): 测试报告对象

    返回:
        str: 格式化的简洁错误消息，如果无法提取则返回 None
    """
    # 优先使用原始错误信息（如果可用），否则使用当前错误信息
    longrepr = getattr(report, '_original_longrepr', report.longrepr)

    if longrepr:
        # 将错误信息转换为字符串（如果它还不是字符串）
        longrepr_str = str(longrepr)

        # 提取测试名称
        test_name = extract_test_name(longrepr_str)

        # 尝试提取 API 信息
        api_name = extract_api_info(longrepr_str)

        # 提取错误消息
        error_message = extract_error_message(longrepr_str)

        # 格式化简洁的错误消息
        if api_name:
            return f"ERROR in {test_name} - API: {api_name}\nError: {error_message}"
        else:
            return f"ERROR in {test_name}\nError: {error_message}"

    return None


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    修改测试报告的 pytest 钩子函数。

    该钩子函数在每个测试执行后被调用，用于捕获测试结果并生成报告。
    对于失败或跳过的测试，会打印格式化的简洁错误摘要，使错误信息更加醒目和易读。

    参数:
        item (pytest.Item): 当前执行的测试项
        call (_pytest.runner.CallInfo): 测试调用信息

    返回:
        None: 该函数不返回值，但会修改测试报告并打印错误摘要
    """
    # 获取测试结果
    outcome = yield
    report = outcome.get_result()

    # 为测试报告添加一个标记，表示它已经被处理过
    # 这有助于防止重复执行
    if not hasattr(report, '_processed'):
        report._processed = True

    # 只处理测试执行阶段（call）的失败或跳过的测试
    if report.when == "call" and (report.failed or report.skipped):
        # 获取简洁的错误消息
        concise_message = get_concise_error_message(report)
        if concise_message:
            # 打印带有红色文本、粗体和醒目格式的简洁错误消息
            print("\n" + "=" * 80)

            # 红色粗体标题
            print("\033[1;31m" + "!!! ERROR SUMMARY !!!" + "\033[0m")
            print("\033[1;31m" + "-" * 80 + "\033[0m")

            # 将消息分行并为每行添加颜色
            for line in concise_message.split('\n'):
                # 添加前缀使其更加醒目
                print("\033[1;31m" + ">>> " + line + "\033[0m")

            print("=" * 80)


# 重写默认的 pytest_report_teststatus 钩子函数，修改测试结果的显示方式
@pytest.hookimpl(trylast=True)
def pytest_report_teststatus(report):
    """
    修改测试状态报告的 pytest 钩子函数。

    该钩子函数用于自定义测试结果在控制台中的显示方式。
    对于失败的测试，我们返回自定义的状态标记，以便与我们的简洁错误报告系统配合使用。

    参数:
        report (pytest.TestReport): 测试报告对象

    返回:
        tuple: 包含测试结果状态、状态字符和状态描述的元组，用于控制台显示
    """
    if report.when == "call" and report.failed:
        # 我们将以自己的方式处理错误显示
        return report.outcome, "F", "FAILED"


# 重写默认的 pytest_terminal_summary 钩子函数，替换标准错误输出
@pytest.hookimpl(trylast=True)
def pytest_terminal_summary(terminalreporter, exitstatus, config):
    """
    修改终端摘要报告的 pytest 钩子函数。

    该钩子函数在所有测试执行完成后被调用，用于生成终端摘要报告。
    我们重写此函数以替换标准的详细错误报告，提示用户查看我们的简洁错误摘要。

    参数:
        terminalreporter (_pytest.terminal.TerminalReporter): 终端报告器对象
        exitstatus (int): 测试运行的退出状态码
        config (pytest.Config): pytest 配置对象

    返回:
        None: 该函数不返回值，但会修改终端输出
    """
    # 我们已经在测试执行期间打印了简洁的错误消息，
    # 所以可以跳过详细的错误报告
    if terminalreporter.stats.get('failed'):
        terminalreporter.write_sep("=", "!!! FAILURES SUMMARY !!!", red=True, bold=True)
        terminalreporter.write_line("详细的错误报告已被上面的简洁错误消息替代。", red=True)
        terminalreporter.write_line("请查看每个测试失败的 !!! ERROR SUMMARY !!! 部分。", red=True)


# 重写默认的 pytest_runtest_logreport 钩子函数，防止显示详细的错误报告
@pytest.hookimpl(trylast=True)
def pytest_runtest_logreport(report):
    """
    修改测试日志报告的 pytest 钩子函数。

    该钩子函数在每个测试的日志报告生成时被调用。
    对于失败的测试，我们保存原始错误信息并替换为简短消息，
    以防止显示冗长的详细错误报告。

    参数:
        report (pytest.TestReport): 测试报告对象

    返回:
        None: 该函数不返回值，但会修改测试报告
    """
    if report.when == "call" and report.failed:
        # 清除 longrepr 以防止显示详细的错误报告
        # 我们会保留原始 longrepr 用于提取简洁的错误消息
        original_longrepr = report.longrepr

        # 将原始 longrepr 存储在自定义属性中供我们使用
        report._original_longrepr = original_longrepr

        # 用简单消息替换 longrepr
        report.longrepr = "详细错误报告已被抑制。请查看上面的 ERROR SUMMARY。"




# 重写 pytest_runtest_protocol 钩子函数，拦截测试执行
@pytest.hookimpl(tryfirst=True)
def pytest_runtest_protocol(item, nextitem):
    """
    自定义 pytest_runtest_protocol 钩子函数实现，用于处理测试重试逻辑。

    该钩子函数在每个测试项执行前被调用，用于控制测试的执行流程。
    我们在这里实现了智能重试逻辑，可以根据错误消息模式决定是否跳过重试。
    同时，我们确保成功的测试不会被重复执行。

    参数:
        item (pytest.Item): 当前要执行的测试项
        nextitem (pytest.Item): 下一个要执行的测试项，可能为 None

    返回:
        bool 或 None:
            - 返回 True 表示我们已处理测试执行，pytest 不应再处理
            - 返回 None 表示让 pytest 正常处理测试执行
    """
    # 从命令行或标记获取重试次数
    reruns = item.config.getoption("reruns", 0)

    # 检查测试是否有 flaky 标记
    flaky_marker = item.get_closest_marker("flaky")
    if flaky_marker is not None:
        # 如果存在，从标记中获取重试次数
        marker_reruns = flaky_marker.kwargs.get("reruns", None)
        if marker_reruns is not None:
            reruns = marker_reruns

    # 如果未指定重试次数，正常运行测试
    if reruns <= 0:
        return None  # 让 pytest 正常处理

    # 运行测试
    reports = runtestprotocol(item, nextitem=nextitem, log=False)

    # 检查测试结果
    has_call_report = False
    has_failed_report = False

    for report in reports:
        if report.when == "call":
            has_call_report = True
            if report.failed:
                has_failed_report = True

    # 如果测试成功执行（有call阶段报告且没有失败），则不需要重试
    if has_call_report and not has_failed_report:
        # 测试成功，不需要重试，让pytest正常处理报告
        # 添加调试信息，帮助理解测试执行流程
        # print(f"\n\033[1;32m>>> 测试 '{item.name}' 成功执行，跳过重试\033[0m")
        return None  # 让pytest正常处理成功的测试结果

    # 让 pytest-rerunfailures 正常处理重试
    return None
