"""
用于测试优化的 Pytest 插件。
"""

import logging
import time
from typing import Dict, Any, Optional, List, Tuple

import pytest
from _pytest.runner import runtestprotocol

from common.test_optimization import (
    register_api_call,
    get_api_call_stats,
    print_api_call_stats,
    clear_function_cache
)
from common.http_utils import (
    clear_cache as clear_http_cache,
    set_cache_max_age
)

"""
Pytest 测试优化插件
"""

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 存储测试执行时间
_test_execution_times: Dict[str, float] = {}


def pytest_configure(config):
    """
    配置 pytest 插件

    参数:
        config: Pytest 配置对象
    """
    logger.info("配置测试优化插件")

    # 注册标记
    config.addinivalue_line(
        "markers", "optimize: 标记需要优化的测试"
    )


def pytest_sessionstart(session):
    """
    在测试会话开始前调用

    参数:
        session: Pytest 会话对象
    """
    logger.info("启动测试优化会话")

    # 在会话开始时清空缓存
    clear_function_cache()
    clear_http_cache()

    # 设置默认缓存时间：常规响应5分钟，token 1小时
    set_cache_max_age(300, 3600)

    # 设置 test_optimization 模块的日志级别为 ERROR 以抑制 INFO 和 WARNING
    logging.getLogger('common.test_optimization').setLevel(logging.ERROR)

    logger.info("已配置优化测试执行的缓存设置")


def pytest_sessionfinish(session, exitstatus):
    """
    在测试会话结束后调用

    参数:
        session: Pytest 会话对象
        exitstatus: 退出状态码
    """
    logger.info("结束测试优化会话")

    # 打印耗时测试
    print_slow_tests()


def pytest_runtest_protocol(item, nextitem):
    """
    执行测试协议

    参数:
        item: 测试项
        nextitem: 下一个测试项

    返回:
        True 如果测试已处理，否则 None
    """
    # 记录开始时间
    start_time = time.time()

    # 运行测试
    reports = runtestprotocol(item, nextitem=nextitem, log=True)

    # 记录执行时间
    execution_time = time.time() - start_time
    _test_execution_times[item.nodeid] = execution_time

    # 记录慢测试
    if execution_time > 1.0:
        logger.warning(f"慢测试: {item.nodeid} 耗时 {execution_time:.2f} 秒")

    # 返回 True 表示测试已经被处理，防止重复执行
    return True


@pytest.hookimpl(tryfirst=True)
def pytest_runtest_setup(item):
    """
    在测试设置之前调用。

    参数:
        item: 测试项
    """
    # 检查测试是否被标记为优化
    optimize_marker = item.get_closest_marker("optimize")
    if optimize_marker is not None:
        logger.info(f"优化测试: {item.nodeid}")


def print_slow_tests(threshold: float = 1.0):
    """
    打印耗时测试用例。

    参数:
        threshold: 耗时测试用例的秒数阈值
    """
    slow_tests = {k: v for k, v in _test_execution_times.items() if v > threshold}

    if slow_tests:
        logger.warning("耗时测试:")
        for test_id, execution_time in sorted(slow_tests.items(), key=lambda x: x[1], reverse=True):
            logger.warning(f"  {test_id}: {execution_time:.2f} 秒")


# 修补 requests 模块以注册 API 调用
try:
    import requests
    original_request = requests.Session.request

    def patched_request(self, method, url, **kwargs):
        """
        经过修补的请求方法，用于注册 API 调用。

        参数:
            method: HTTP 请求方法
            url: 请求的 URL
            **kwargs: 额外的请求参数

        返回:
            原始请求方法的响应
        """
        # 注册 API 调用
        register_api_call(url, method, **kwargs)

        # 调用原始请求方法
        return original_request(self, method, url, **kwargs)

    # 应用修补程序
    requests.Session.request = patched_request
    logger.info("已修补 requests.Session.request 以注册 API 调用")

except ImportError:
    logger.warning("修补 requests.Session.request 失败：未找到 requests 模块")
