# time_utils.py

from datetime import datetime, timed<PERSON><PERSON>


def parse_and_compare_time(time_str):
    """
    解析时间字符串并比较是否在当前时间的4个小时以内。

    :param time_str: 时间的字符串表示，格式需与time_format参数匹配
    :return: 如果时间在4小时内返回True，否则返回False
    """
    time_format = "%Y-%m-%d %H:%M:%S"
    last_update_time = datetime.strptime(time_str, time_format)
    current_time = datetime.now()
    
    time_difference = abs(current_time - last_update_time)
    four_hours = timedelta(hours=4)
    
    is_within_range = time_difference <= four_hours
    
    if not is_within_range:
        print(f"时间差超过4小时: 检查时间={last_update_time}, 当前时间={current_time}, 差值={time_difference}")
    
    return is_within_range
