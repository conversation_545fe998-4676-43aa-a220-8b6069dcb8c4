#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time   : 2024/8/26 14:49
# <AUTHOR> kuuuuu

"""
Assert 断言类型
"""
from datetime import datetime, timedelta
from typing import Any, Union, Text
import logging

def equals(
        check_value: Any, expect_value: Any, message: Text = ""
):
    """判断是否相等"""

    assert check_value == expect_value, message


def time_within_hours(
        check_time: Union[datetime, str],
        expect_time: Union[datetime, str],
        hours: Union[int, float],
        message: Text = ""
):
    """
    判断一个时间是否在另一个时间的指定小时范围内
    """
    # 将字符串时间转换为datetime对象，如果已经是datetime对象则直接使用
    if isinstance(check_time, str):
        check_time = datetime.strptime(check_time, "%Y-%m-%d %H:%M:%S")
    if isinstance(expect_time, str):
        expect_time = datetime.strptime(expect_time, "%Y-%m-%d %H:%M:%S")

    # 计算时间差
    time_difference = abs(check_time - expect_time)

    # 断言时间差是否在指定的小时范围内
    assert time_difference <= timedelta(hours=hours), message


def less_than(
        check_value: Union[int, float], expect_value: Union[int, float], message: Text = ""
):
    """判断实际结果小于预期结果"""
    assert check_value < expect_value, message


def less_than_or_equals(
        check_value: Union[int, float], expect_value: Union[int, float], message: Text = ""):
    """判断实际结果小于等于预期结果"""
    assert check_value <= expect_value, message


def greater_than(
        check_value: Union[int, float], expect_value: Union[int, float], message: Text = ""
):
    """判断实际结果大于预期结果"""
    assert check_value > expect_value, message


def greater_than_or_equals(
        check_value: Union[int, float], expect_value: Union[int, float], message: Text = ""
):
    """判断实际结果大于等于预期结果"""
    assert check_value >= expect_value, message


def not_equals(
        check_value: Any, expect_value: Any, message: Text = ""
):
    """判断实际结果不等于预期结果"""
    assert check_value != expect_value, message


def string_equals(
        check_value: Text, expect_value: Any, message: Text = ""
):
    """判断字符串是否相等"""
    assert check_value == expect_value, message


def length_equals(
        check_value: Text, expect_value: int, message: Text = ""
):
    """判断长度是否相等"""
    assert isinstance(
        expect_value, int
    ), "expect_value 需要为 int 类型"
    assert len(check_value) == expect_value, message


def length_greater_than(
        check_value: Text, expect_value: Union[int, float], message: Text = ""
):
    """判断长度大于"""
    assert isinstance(
        expect_value, (float, int)
    ), "expect_value 需要为 float/int 类型"
    assert len(str(check_value)) > expect_value, message


def length_greater_than_or_equals(
        check_value: Text, expect_value: Union[int, float], message: Text = ""
):
    """判断长度大于等于"""
    assert isinstance(
        expect_value, (int, float)
    ), "expect_value 需要为 float/int 类型"
    assert len(check_value) >= expect_value, message


def length_less_than(
        check_value: Text, expect_value: Union[int, float], message: Text = ""
):
    """判断长度小于"""
    assert isinstance(
        expect_value, (int, float)
    ), "expect_value 需要为 float/int 类型"
    assert len(check_value) < expect_value, message


def length_less_than_or_equals(
        check_value: Text, expect_value: Union[int, float], message: Text = ""
):
    """判断长度小于等于"""
    assert isinstance(
        expect_value, (int, float)
    ), "expect_value 需要为 float/int 类型"
    assert len(check_value) <= expect_value, message


def contains(check_value: Any, expect_value: Any, message: Text = ""):
    """判断期望结果内容包含在实际结果中"""
    assert isinstance(
        check_value, (list, tuple, dict, str, bytes)
    ), "expect_value 需要为  list/tuple/dict/str/bytes  类型"
    assert expect_value in check_value, message


def contained_by(check_value: Any, expect_value: Any, message: Text = ""):
    """判断实际结果包含在期望结果中"""
    assert isinstance(
        expect_value, (list, tuple, dict, str, bytes)
    ), "expect_value 需要为  list/tuple/dict/str/bytes  类型"

    assert check_value in expect_value, message


def startswith(
        check_value: Any, expect_value: Any, message: Text = ""
):
    """检查响应内容的开头是否和预期结果内容的开头相等"""
    assert str(check_value).startswith(str(expect_value)), message


def endswith(
        check_value: Any, expect_value: Any, message: Text = ""
):
    """检查响应内容的结尾是否和预期结果内容相等"""
    assert str(check_value).endswith(str(expect_value)), message


def assert_no_error_indicators_in_response(
        response_text: Any, custom_message: Text = ""
):
    """检查响应中是否包含错误指示符"""
    if not isinstance(response_text, str):
        error_message = f'Assertion failed: Expected a string response, but got {type(response_text)}. Response: {response_text}'
        if custom_message:
            error_message = f"{custom_message}: {error_message}"
        logging.error(error_message)
        raise AssertionError(error_message)

    error_indicators_ci = ['error', 'fail', 'exception', 'traceback', 'problem']
    error_indicators_cs = ['400', '401', '403', '404', '500', '502', '503', '504', '接口异常']

    found_errors = []
    res_lower = response_text.lower()

    for indicator in error_indicators_ci:
        if indicator in res_lower:
            found_errors.append(f"Found case-insensitive: '{indicator}'")

    for indicator in error_indicators_cs:
        if indicator in response_text:
            found_errors.append(f"Found case-sensitive: '{indicator}'")

    if found_errors:
        response_snippet = (response_text[:500] + '...') if len(response_text) > 500 else response_text
        error_message_intro = 'Assertion failed: Found error indicators in response: '
        error_details = ', '.join(found_errors)
        error_message_context = '. Response snippet: '
        final_error_message = error_message_intro + error_details + error_message_context + response_snippet
        if custom_message:
            final_error_message = f"{custom_message}: {final_error_message}"

        logging.error(final_error_message)
        raise AssertionError(final_error_message)
