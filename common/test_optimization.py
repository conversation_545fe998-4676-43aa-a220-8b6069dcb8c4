"""
测试优化工具，用于提高测试执行速度。
"""

import functools
import inspect
import logging
import time
from typing import Dict, Any, Callable, Optional, List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 函数结果的全局缓存
_function_cache: Dict[str, Any] = {}
_api_call_registry: Dict[str, Dict[str, Any]] = {}


def get_cache_key(func: Callable, args: Tuple, kwargs: Dict[str, Any]) -> str:
    """
    为函数调用生成缓存键。

    参数:
        func: 被调用的函数
        args: 位置参数
        kwargs: 关键字参数

    返回:
        表示函数调用的字符串
    """
    # 获取函数的模块和名称
    func_name = f"{func.__module__}.{func.__qualname__}"

    # 将args和kwargs转换为字符串
    args_str = str(args)
    kwargs_str = str(sorted(kwargs.items()))

    # 组合创建唯一键
    return f"{func_name}:{args_str}:{kwargs_str}"


def memoize(func: Callable) -> Callable:
    """
    用于记忆函数结果的装饰器。

    参数:
        func: 需要记忆的函数

    返回:
        带有记忆功能的包装函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 为此函数调用生成缓存键
        cache_key = get_cache_key(func, args, kwargs)

        # 如果有缓存结果则返回
        if cache_key in _function_cache:
            logger.debug(f"缓存命中: {func.__name__}")
            return _function_cache[cache_key]

        # 调用函数并缓存结果
        result = func(*args, **kwargs)
        _function_cache[cache_key] = result

        return result

    return wrapper


def register_api_call(url: str, method: str, **kwargs) -> Dict[str, Any]:
    """
    注册API调用以防止重复。

    参数:
        url: API URL
        method: HTTP方法
        **kwargs: 额外的请求参数

    返回:
        包含API调用信息的字典
    """
    # 为此API调用创建键
    call_key = f"{method.lower()}:{url}"

    # 注册调用
    if call_key not in _api_call_registry:
        _api_call_registry[call_key] = {
            'url': url,
            'method': method,
            'kwargs': kwargs,
            'count': 1,
            'first_call_time': time.time()
        }
    else:
        _api_call_registry[call_key]['count'] += 1

    return _api_call_registry[call_key]


def optimize_test_fixture(fixture_func: Callable) -> Callable:
    """
    通过缓存结果优化测试夹具的装饰器。

    参数:
        fixture_func: 需要优化的夹具函数

    返回:
        带有优化功能的包装夹具函数
    """
    @functools.wraps(fixture_func)
    def wrapper(*args, **kwargs):
        # 检查这是否是pytest夹具
        if hasattr(fixture_func, '_pytestfixturefunction'):
            # 为此夹具调用生成缓存键
            cache_key = get_cache_key(fixture_func, args, kwargs)

            # 如果有缓存结果则返回
            if cache_key in _function_cache:
                logger.debug(f"夹具缓存命中: {fixture_func.__name__}")
                return _function_cache[cache_key]

            # 调用夹具并缓存结果
            result = fixture_func(*args, **kwargs)
            _function_cache[cache_key] = result

            return result
        else:
            # 不是pytest夹具，直接调用函数
            return fixture_func(*args, **kwargs)

    return wrapper


def clear_function_cache() -> None:
    """清除函数缓存以释放内存。"""
    global _function_cache
    _function_cache.clear()
    logger.info("函数缓存已清除")


def get_api_call_stats() -> Dict[str, Dict[str, Any]]:
    """
    获取API调用的统计信息。

    返回:
        包含API调用统计信息的字典
    """
    return _api_call_registry


def print_api_call_stats() -> None:
    """打印API调用的统计信息。"""
    logger.info("API调用统计:")

    for call_key, call_info in _api_call_registry.items():
        logger.info(f"  {call_info['method'].upper()} {call_info['url']}: {call_info['count']} 次调用")

    # 打印重复调用
    duplicates = {k: v for k, v in _api_call_registry.items() if v['count'] > 1}
    if duplicates:
        logger.warning("重复的API调用:")
        for call_key, call_info in duplicates.items():
            logger.warning(f"  {call_info['method'].upper()} {call_info['url']}: {call_info['count']} 次调用")


def optimize_api_calls(func: Callable) -> Callable:
    """
    通过防止重复优化API调用的装饰器。

    参数:
        func: 需要优化的函数

    返回:
        带有API调用优化的包装函数
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 检查这是否是测试函数
        if func.__name__.startswith('test_'):
            # 注册测试开始
            logger.debug(f"开始测试 {func.__name__}")

            # 调用函数
            result = func(*args, **kwargs)

            # 测试后打印API调用统计
            print_api_call_stats()

            return result
        else:
            # 不是测试函数，直接调用函数
            return func(*args, **kwargs)

    return wrapper
