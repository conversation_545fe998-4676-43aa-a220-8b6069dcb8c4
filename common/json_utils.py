# encoding='utf-8'
import json
import os
import time
import hashlib
import logging
from functools import lru_cache
from typing import Dict, Any, Optional, Union, List
import ijson

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# JSON文件的全局缓存
_json_cache: Dict[str, Dict[str, Any]] = {}
_json_cache_timestamp: Dict[str, float] = {}
_json_cache_max_age = 300  # 5分钟缓存有效期


class JsonUtils:
    """
    优化的JSON文件工具类，具有缓存、流式处理和错误处理功能。
    """

    @staticmethod
    def get_file_hash(file_path: str) -> str:
        """
        为文件路径生成哈希值，用作缓存键。

        参数:
            file_path: JSON文件的路径

        返回:
            表示文件路径的哈希字符串
        """
        return hashlib.md5(file_path.encode()).hexdigest()

    @staticmethod
    def is_cache_valid(file_path: str) -> bool:
        """
        检查文件的缓存数据是否仍然有效。

        参数:
            file_path: JSON文件的路径

        返回:
            如果缓存有效则为True，否则为False
        """
        file_hash = JsonUtils.get_file_hash(file_path)

        # 检查文件是否存在于缓存中
        if file_hash not in _json_cache:
            return False

        # 检查文件自缓存后是否被修改
        try:
            file_mtime = os.path.getmtime(file_path)
            cache_time = _json_cache_timestamp.get(file_hash, 0)

            # 如果文件在缓存后被修改或缓存太旧
            if file_mtime > cache_time or (time.time() - cache_time) > _json_cache_max_age:
                return False

            return True
        except (OSError, IOError) as e:
            logger.warning(f"Error checking file modification time: {e}")
            return False

    @staticmethod
    def load_json(file_path: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        从文件加载JSON数据，支持缓存。

        参数:
            file_path: JSON文件的路径
            use_cache: 是否使用缓存（默认：True）

        返回:
            解析后的JSON数据（字典）

        异常:
            FileNotFoundError: 如果文件不存在
            json.JSONDecodeError: 如果文件包含无效的JSON
        """
        file_hash = JsonUtils.get_file_hash(file_path)

        # 如果缓存有效，则返回缓存数据
        if use_cache and JsonUtils.is_cache_valid(file_path):
            return _json_cache[file_hash]

        try:
            # 对于小文件，一次性加载整个文件
            if os.path.getsize(file_path) < 1024 * 1024:  # 小于1MB
                with open(file_path, 'r', encoding='utf-8') as fp:
                    data = json.load(fp)
            else:
                # 对于大文件，使用流式解析器
                data = JsonUtils.load_json_streaming(file_path)

            # 更新缓存
            if use_cache:
                _json_cache[file_hash] = data
                _json_cache_timestamp[file_hash] = time.time()

            return data

        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in file {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading JSON file {file_path}: {e}")
            raise

    @staticmethod
    def load_json_streaming(file_path: str) -> Dict[str, Any]:
        """
        使用流式解析器加载大型JSON文件。

        参数:
            file_path: JSON文件的路径

        返回:
            解析后的JSON数据（字典）
        """
        result = {}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                parser = ijson.parse(f)
                current_path = []
                current_obj = result

                for prefix, event, value in parser:
                    if event == 'start_map':
                        if prefix:
                            new_obj = {}
                            if current_path:
                                # 导航到结果字典中的当前位置
                                temp = result
                                for key in current_path[:-1]:
                                    temp = temp[key]
                                temp[current_path[-1]] = new_obj
                            current_obj = new_obj
                    elif event == 'end_map':
                        if current_path:
                            current_path.pop()
                    elif event == 'map_key':
                        current_path.append(value)
                    elif event == 'string' or event == 'number' or event == 'boolean' or event == 'null':
                        if current_path:
                            # 导航到结果字典中的当前位置
                            temp = result
                            for key in current_path[:-1]:
                                temp = temp[key]
                            temp[current_path[-1]] = value

            return result
        except Exception as e:
            logger.error(f"Error parsing JSON stream from {file_path}: {e}")
            # 回退到常规加载方式
            with open(file_path, 'r', encoding='utf-8') as fp:
                return json.load(fp)

    @staticmethod
    def get_data_path(folder: str, file_name: str) -> str:
        """
        获取JSON数据文件的绝对路径。

        参数:
            folder: 相对于项目根目录的文件夹路径
            file_name: JSON文件名

        返回:
            JSON文件的绝对路径
        """
        base_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
        file_path = os.path.join(base_path, folder, file_name)
        return file_path

    @staticmethod
    @lru_cache(maxsize=128)
    def get_json_value(file_path: str, key: str, use_cache: bool = True) -> Any:
        """
        通过键从JSON文件获取特定值，支持缓存。

        参数:
            file_path: JSON文件的路径
            key: 从JSON数据中检索的键
            use_cache: 是否使用缓存（默认：True）

        返回:
            与键关联的值

        异常:
            KeyError: 如果键在JSON数据中不存在
        """
        data = JsonUtils.load_json(file_path, use_cache)

        try:
            return data[key]
        except KeyError:
            logger.error(f"Key '{key}' not found in JSON file {file_path}")
            raise

    @staticmethod
    def query_json_path(data: Dict[str, Any], json_path: str) -> Any:
        """
        使用简化的路径语法查询JSON数据。

        参数:
            data: 作为字典的JSON数据
            json_path: 到所需值的路径（例如，"user.address.city"）

        返回:
            指定路径的值，如果未找到则返回None
        """
        if not json_path:
            return data

        parts = json_path.split('.')
        current = data

        for part in parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return None

        return current

    @staticmethod
    def clear_cache() -> None:
        """清除JSON缓存以释放内存。"""
        global _json_cache, _json_cache_timestamp
        _json_cache.clear()
        _json_cache_timestamp.clear()
        logger.info("JSON cache cleared")

    @staticmethod
    def set_cache_max_age(seconds: int) -> None:
        """
        设置缓存JSON数据的最大有效期。

        参数:
            seconds: 最大有效期（秒）
        """
        global _json_cache_max_age
        _json_cache_max_age = seconds
        logger.info(f"JSON cache max age set to {seconds} seconds")


class JsonFileReader:
    """
    用于读取和访问JSON文件的类，支持缓存。
    与原始ReadJsonFileUtils接口兼容。
    """

    def __init__(self, file_name: str):
        """
        初始化JSON文件读取器。

        参数:
            file_name: JSON文件的路径
        """
        self.file_name = file_name
        self.data = self.get_data()

    def get_data(self) -> Dict[str, Any]:
        """
        从文件加载JSON数据。

        返回:
            解析后的JSON数据（字典）
        """
        return JsonUtils.load_json(self.file_name)

    def get_value(self, id: str) -> Any:
        """
        通过键从JSON数据获取值。

        参数:
            id: 从JSON数据中检索的键

        返回:
            与键关联的值
        """
        return self.data[id]

    @staticmethod
    def get_data_path(folder: str, file_name: str) -> str:
        """
        获取JSON数据文件的绝对路径。

        参数:
            folder: 相对于项目根目录的文件夹路径
            file_name: JSON文件名

        返回:
            JSON文件的绝对路径
        """
        return JsonUtils.get_data_path(folder, file_name)


# 向后兼容函数
def get_json_file(system: str, file_name: str) -> Dict[str, Any]:
    """
    从特定系统文件夹中的文件加载JSON数据。
    与原始getfile函数兼容。

    参数:
        system: 系统文件夹名称
        file_name: JSON文件名

    返回:
        解析后的JSON数据（字典）
    """
    from common import setting

    file_path = os.path.join(setting.DATA_PATH, system, file_name)
    return JsonUtils.load_json(file_path)
