"""
认证管理器，用于集中管理令牌。
本模块提供一个单例，用于在测试中管理认证令牌。
"""

import logging
import time
from typing import Dict, Any, Optional, Tuple
import threading

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 带锁的令牌缓存，保证线程安全
_token_cache: Dict[str, Dict[str, Any]] = {}
_token_cache_lock = threading.RLock()
_token_cache_max_age = 3600  # 默认令牌有效期为1小时


def get_cache_key(url: str, credentials: Dict[str, Any]) -> str:
    """
    生成认证凭证的缓存键
    
    参数:
        url: 认证URL
        credentials: 认证凭证
        
    返回:
        表示认证请求的字符串
    """
    # 对凭证排序以确保生成一致的键
    sorted_creds = sorted(
        [(k, v) for k, v in credentials.items() if k not in ['token']],
        key=lambda x: x[0]
    )
    
    # 基于URL和凭证创建唯一键
    creds_str = ','.join([f"{k}={v}" for k, v in sorted_creds])
    return f"{url}:{creds_str}"


def is_token_valid(cache_key: str) -> bool:
    """
    检查缓存的令牌是否仍然有效
    
    参数:
        cache_key: 令牌的缓存键
        
    返回:
        如果令牌有效返回True，否则返回False
    """
    with _token_cache_lock:
        # 检查令牌是否存在于缓存中
        if cache_key not in _token_cache:
            return False
            
        # 检查令牌是否过期
        token_time = _token_cache[cache_key].get('timestamp', 0)
        if (time.time() - token_time) > _token_cache_max_age:
            return False
            
        return True


def get_token(url: str, credentials: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    从缓存获取有效的令牌
    
    参数:
        url: 认证URL
        credentials: 认证凭证
        
    返回:
        缓存的令牌数据，如果未找到或无效则返回None
    """
    cache_key = get_cache_key(url, credentials)
    
    if is_token_valid(cache_key):
        with _token_cache_lock:
            logger.debug(f"Using cached token for {url}")
            return _token_cache[cache_key].get('data')
            
    return None


def save_token(url: str, credentials: Dict[str, Any], token_data: Dict[str, Any]) -> None:
    """
    将令牌保存到缓存
    
    参数:
        url: 认证URL
        credentials: 认证凭证
        token_data: 要缓存的令牌数据
    """
    cache_key = get_cache_key(url, credentials)
    
    with _token_cache_lock:
        _token_cache[cache_key] = {
            'data': token_data,
            'timestamp': time.time()
        }
        logger.debug(f"Cached token for {url}")


def clear_token_cache() -> None:
    """清空令牌缓存"""
    with _token_cache_lock:
        _token_cache.clear()
        logger.info("Token cache cleared")


def set_token_max_age(seconds: int) -> None:
    """
    设置缓存令牌的最大有效期
    
    参数:
        seconds: 最大有效期(秒)
    """
    global _token_cache_max_age
    _token_cache_max_age = seconds
    logger.info(f"Token cache max age set to {seconds} seconds")


def is_auth_url(url: str) -> bool:
    """
    检查URL是否为认证端点
    
    参数:
        url: 要检查的URL
        
    返回:
        如果是认证端点返回True，否则返回False
    """
    # 已知的认证端点列表
    auth_endpoints = [
        'login',
        'token',
        'auth',
        'pwd/login',
        'access/token',
        'oauth/login',
        'authorization'
    ]
    
    # 检查URL是否包含任何认证端点
    return any(endpoint in url for endpoint in auth_endpoints)


def is_auth_request(method: str, url: str, **kwargs) -> bool:
    """
    检查请求是否为认证请求
    
    参数:
        method: HTTP方法
        url: 请求URL
        **kwargs: 额外请求参数
        
    返回:
        如果是认证请求返回True，否则返回False
    """
    # 检查方法是否为POST(大多数认证请求是POST)
    if method.lower() != 'post':
        return False
        
    # 检查URL是否为认证端点
    return is_auth_url(url)
