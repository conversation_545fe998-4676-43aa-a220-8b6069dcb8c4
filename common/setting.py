import os, sys

BASE_DIR = os.path.dirname(os.path.dirname(__file__))
sys.path.append(BASE_DIR)

# 配置文件
TEST_CONFIG = os.path.join(BASE_DIR, "config", "config.ini")
# 数据库配置文件
TEST_CONFIG_DB = os.path.join(BASE_DIR, "config", "config_mysql.ini")
# 测试用例报告
TEST_REPORT = os.path.join(BASE_DIR, "report")
# 测试用例程序文件
TEST_CASE = os.path.join(BASE_DIR, "testcase")
# sql文件
SQL_PATH = os.path.join(BASE_DIR, "config", "jiwei.sql")
# 测试用例数据
DATA_PATH = os.path.join(BASE_DIR, "data")
# 全局变量
GLOBALDATA_PATH = os.path.join(BASE_DIR, "global_data/")
