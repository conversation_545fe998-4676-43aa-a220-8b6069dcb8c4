import os
import json
import datetime
import shutil

# 配置路径
test_path = "testcase"
allure_xml_path = "./allure-report/json"
allure_path = "./allure-report/html"
current_date = datetime.datetime.now().strftime('%Y%m%d')
build_order = 1


def create_directory(path):
    """创建目录，如果目录不存在的话"""
    if not os.path.exists(path):
        os.makedirs(path)


def get_next_directory_number():
    """获取下一个目录编号"""
    report_dirs = os.listdir(allure_path)
    if not report_dirs:
        return 1, None
    else:
        report_dirs.sort(key=lambda x: os.path.getmtime(os.path.join(allure_path, x)))
        last_dir = report_dirs[-1]
        number = int(last_dir.split('-')[-1])  # 获取最后一个目录的编号
        history_file = os.path.join(allure_path, last_dir, 'widgets', 'history-trend.json')
        with open(history_file) as f:
            data = f.read()
        return number + 1, data


def update_single_result(build_order):
    """更新单次测试结果"""
    new_single_file = os.path.join(allure_path, f'{current_date}-{build_order}', 'widgets', 'history-trend.json')
    with open(new_single_file, 'r+') as f:
        data = json.load(f)
        data[0]["buildOrder"] = build_order
        # 假设报告URL格式已知且固定
        data[0][
            'reportUrl'] = f'http://localhost:63342/automated-test/reports/html/{current_date}-{build_order}/index.html'
        f.seek(0)  # 移动到文件开头
        json.dump(data, f, indent=4)  # 使用indent美化输出


def update_history_trend(build_order):
    """更新历史趋势数据"""
    all_result_dirs = os.listdir(allure_path)
    sorted_dirs = sorted(all_result_dirs, key=lambda x: os.path.getmtime(os.path.join(allure_path, x)))
    old_data_path = os.path.join(allure_path, sorted_dirs[-2], 'widgets', 'history-trend.json')
    new_data_path = os.path.join(allure_path, sorted_dirs[-1], 'widgets', 'history-trend.json')

    with open(old_data_path, 'r') as f:
        old_data = json.load(f)
    with open(new_data_path, 'r') as f:
        new_data = json.load(f)

    # 合并历史数据和新数据
    combined_data = old_data + new_data
    with open(new_data_path, 'w') as f:
        json.dump(combined_data, f, indent=4)


def clear_old_reports(day_of_week):
    """如果今天是周六，则清空历史测试报告"""
    if day_of_week == 5:
        shutil.rmtree(allure_path)
        shutil.rmtree(allure_xml_path)


def run_allure_tests():

    print(
        """
                         _    _         _      _____         _
          __ _ _ __ (_)  / \\  _   _| |_ __|_   _|__  ___| |_
         / _` | '_ \\| | / _ \\| | | | __/ _ \\| |/ _ \\/ __| __|
        | (_| | |_) | |/ ___ \\ |_| | || (_) | |  __/\\__ \\ |_
         \\__,_| .__/|_/_/   \\_\\__,_|\\__\\___/|_|\\___||___/\\__|
              |_|
              开始执行{}项目...
            """
    )

    create_directory(allure_path)
    create_directory(allure_xml_path)

    day_of_week = datetime.datetime.now().weekday()
    clear_old_reports(day_of_week)

    build_order, old_data = get_next_directory_number()
    command = f'pytest {test_path} -s --alluredir={os.path.join(allure_xml_path, f"{current_date}-{build_order}")}'
    os.system(command)

    command1 = f'allure generate {os.path.join(allure_xml_path, f"{current_date}-{build_order}")} -o {os.path.join(allure_path, f"{current_date}-{build_order}")} --clean'
    os.system(command1)

    update_single_result(build_order)
    if len(os.listdir(allure_path)) != 1:
        update_history_trend(build_order)


if __name__ == "__main__":
    run_allure_tests()
