{"permissions": {"allow": ["Bash(rm /Users/<USER>/PycharmProjects/automated-test/common/__init__.py)", "Bash(rm /Users/<USER>/PycharmProjects/automated-test/common/util/__init__.py)", "Bash(rm /Users/<USER>/PycharmProjects/automated-test/common/pytest_plugins/__init__.py)", "Bash(rm /Users/<USER>/PycharmProjects/automated-test/common/util/dict_chunk.py)", "Bash(rm /Users/<USER>/PycharmProjects/automated-test/common/util/log_model.py)", "Bash(rm /Users/<USER>/PycharmProjects/automated-test/common/util/string_util.py)", "Bash(mv /Users/<USER>/PycharmProjects/automated-test/common/pytest_plugins/test_optimization.py /Users/<USER>/PycharmProjects/automated-test/common/pytest_plugins/pytest_optimization_plugin.py)", "Bash(find /Users/<USER>/PycharmProjects/automated-test -name \"__pycache__\" -type d)", "Bash(find /Users/<USER>/PycharmProjects/automated-test -name \"__pycache__\" -type d -exec rm -rf {} +)", "<PERSON><PERSON>(true)", "Bash(find /Users/<USER>/PycharmProjects/automated-test -name \"*.pyc\" -type f -delete)", "Bash(python -c \"import common.pytest_plugins.pytest_optimization_plugin; print(''插件导入成功'')\")", "Bash(python -m pytest --collect-only -q)"], "deny": []}}