#!/usr/bin/env python
# _*_ coding:utf-8 _*_
import os,sys
import pymysql
import subprocess
sys.path.append(os.path.dirname(__file__))
from common import setting
import run

#报告存放地址
# now = time.strftime("%Y-%m-%d %H_%M_%S")
# file = open(setting.TEST_REPORT + '/' + "自动化测试报告" + now + '.html','wb')
# run = HTMLTestRunner.HTMLTestRunner(stream=file, title='测试报告', description='执行结果')
# dis = unittest.defaultTestLoader.discover(setting.TEST_CASE, pattern='test*.py')
#
# run.run(dis)
# file.close()
powershell_command = r"Get-ChildItem -Path . -Include __pycache__ -Recurse | ForEach-Object { Remove-Item -Recurse -Force $_.FullName }"
result = subprocess.run(["powershell.exe", powershell_command], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)



run.test_allure()

try:
    database = pymysql.connect(database="jiwei",user="work",password="MDa61Obr8EE6ASnl",host="*************",port=3306)
    c = database.cursor()
    with open(setting.SQL_PATH,encoding='utf-8',mode='r') as f:
        # 读取整个sql文件，以分号切割。[:-1]删除最后一个元素，也就是空字符串
        sql_list = f.read().split(';')[:-1]
        for x in sql_list:
            # sql语句添加分号结尾
            sql_item = x+';'
            c.execute(sql_item)
            print("执行成功sql: %s"%sql_item)
except Exception as e:
    print(e)
    print('执行失败sql: %s'%sql_item)
finally:
    # 关闭mysql连接
    c.close()
    database.commit()
    database.close()