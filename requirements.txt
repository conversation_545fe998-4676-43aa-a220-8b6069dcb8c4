# Testing Framework
allure-pytest==2.10.0
allure-python-commons==2.10.0
pytest==7.2.0
pytest-forked==1.3.0
pytest-ordering==0.6
pytest-rerunfailures==12.0
pytest-xdist==3.3.1

# HTTP and API
requests==2.32.4
urllib3==2.5.0
chardet==3.0.4
charset-normalizer==2.1.1
pyOpenSSL==21.0.0

# JSON Processing
ijson==3.2.3
jsonpath==0.82

# Database
PyMySQL==1.1.1
SQLAlchemy==1.4.41
DBUtils==3.0.2

# HTML/XML Processing
beautifulsoup4==4.11.1
lxml==4.9.1
soupsieve==2.3.2.post1

# Configuration and Utilities
PyYAML==6.0
colorama==0.4.6
psutil==5.9.5
iso8601==1.0.2
ddt==1.4.4

# Template Engine (Updated to resolve conflict)
Jinja2>=3.1.6
MarkupSafe>=2.1.1

# Testing Support
async-generator==1.10
atomicwrites==1.4.0
exceptiongroup==1.0.0rc9
execnet==1.9.0
h11==0.12.0
iniconfig==1.1.1
outcome==1.1.0
packaging==21.3
pluggy==1.0.0
py==1.11.0
pyparsing==3.0.9
six==1.16.0
sniffio==1.2.0
sortedcontainers==2.4.0
