'''会议签到系统'''

# 登录
ACP_checkin_login_url = "http://47.107.239.240:8081"

# 会议签到系统url
ACP_checkin_url = "http://47.107.239.240:8081"


# 会议签到系统
ACP_checkin_login = {
    "login": "/api/access/token",                           # 登录
    'GET_activity' : '/api/activity/d1609ea2',              # 根据code获取活动相关信息
    'POST_guest':'/api/guest',                              # 创建报名        
    'GET_guest':'/api/guest',                               # 报名列表筛选
    'GET_resource_guest_type':'/api/resource/guest_type',   # 获取人员类型
    'PUT_guest':'/api/guest/{uuid}',                        # 修改嘉宾信息
    'GET_details_guest':'/api/guest/{uuid}',                # 查看嘉宾详情信息
    'POST_sign_guest':'/api/guest/{uuid}/checkin_log',      # 嘉宾签到信息
    'GET_guest_type':'/api/report/guest_type',              # 查询签到统计
    'GET_guest_permission':'/api/report/guest_permission',  # 查询权限统计
    'GET_guest_place':'/api/report/guest_place',            # 查询场地统计
    'GET_company_search' : '/api/company/search',           # 企业联想搜索（创建报名）
    'GET_menu':'/api/menu',                                 # 获取权限菜单
    'GET_room': '/api/room',                                # 获取会议展厅信息
    'GET_guest_tags': '/api/guest/tags',                    # 创建报名-获取标签列表
    'GET_resource_condition':'/api/resource/condition',     # 获取报名列表搜索条件
    'GET_guest_report_guest':'/api/guest/report_guest',     # 获取统计列表页嘉宾信息
    # /api/place                                            # 根据code获取签到活动的场地信息列表
    # /api/activit                                          # 获取当前活动列表
    # 手机端接口、签到机相关接口未整理其中



}


