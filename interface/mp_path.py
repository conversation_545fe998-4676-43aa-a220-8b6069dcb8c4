base_url = "http://data.ijiwei.com/soya/apps/doc/server"
baseurl_short = "data.ijiwei.com"

# 企业库登录
mp_login = {
    "login": "/?action=user1.loginByVerifyCode2",
}

# 中台接口集
api_path = {
    # 企业库
    "companyList": "?action=enterprise.companyList",
    "typeList": "?action=enterprise.typeList",
    "statusList": "?action=enterprise.statusList",
    "roundList": "?action=enterprise.roundList&_driver=mp",
    "getCompanyInfo": "/?action=enterprise.getCompanyInfo",
    "getKtAnnouncementList": "/?action=enterprise.getKtAnnouncementList",
    "getCourtAnnouncementList": "/?action=enterprise.getCourtAnnouncementList",
    "getCourtRegisterList": "/?action=enterprise.getCourtRegisterList",
    "getLawSuit": "/?action=enterprise.getLawSuit",
    "trademarkList": "/?action=enterprise.trademarkList",
    "patentList": "/?action=enterprise.patentList",
    "getHolderList": "/?action=enterprise.getHolderList",
    "getBidding": "/?action=enterprise.getBidding",
    "AbnormalList": "/?action=enterprise.AbnormalList",
    "illegalRecordList": "/?action=enterprise.illegalRecordList",
    "administrativePenaltyList": "/?action=enterprise.administrativePenaltyList",
    "getAntitrustList": "/?action=enterprise.getAntitrustList",
    "getCompanyRecruit": "/?action=enterprise.getCompanyRecruit",
    "investmentList": "/?action=enterprise.investmentList",
    "financingList": "/?action=enterprise.financingList",
    "getCompanyUser": "/?action=enterprise.getCompanyUser",
    "AnnouncementReportList": "/?action=enterprise.AnnouncementReportList",
    "ProductList": "/?action=enterprise.ProductList",
    "getChangeList": "/?action=enterprise.getChangeList",
    "CertificateList": "/?action=enterprise.CertificateList",
    "SupplyList": "/?action=enterprise.SupplyList",
    "CustomerList": "/?action=enterprise.CustomerList",
    "financeReport": "/?action=enterprise.financeReport",
    "ipoList": "/?action=enterprise.ipoList",
    "getKtAnnouncementInfo": "?action=enterprise.getKtAnnouncementInfo",
    "getLawSuitDetail": "?action=enterprise.getLawSuitDetail",
    "patentDetail": "?action=enterprise.patentDetail",
    "getCourtAnnouncementInfo": "?action=enterprise.getCourtAnnouncementInfo",
    "getCourtRegisterInfo": "?action=enterprise.getCourtRegisterInfo",
    "getBiddingDetail": "?action=enterprise.getBiddingDetail",
    # 舆情订阅
    "getOpinion": "/?action=enterprise.getOpinion",
    "zcfzbDate": "/?action=enterprise.zcfzbDate",
    "zcfzbList": "/?action=enterprise.zcfzbList",
    "lrbList": "/?action=enterprise.lrbList",
    "lrbDate": "/?action=enterprise.lrbDate",
    "zyzblist": "/?action=enterprise.zyzblist",
    "addUserFeedback": "/?action=enterprise.addUserFeedback&_driver=mp",

    "companyGroupList": "/?action=front.companyGroupList",
    "searchCompany": "/?action=front.searchCompany&_driver=mp&name=&isAll=1",

    "searchCompany": "/?action=front.searchCompany&_driver=mp&name=&isAll=bookreport1",
    "trendCategory": "/?action=front.trendCategory",
    "trendList": "/?action=front.trendList",
    "addCompanyGroup": "/?action=front.addCompanyGroup",
    "delCompanyGroup": "/?action=front.delCompanyGroup",
    "companyTrendList": "/?action=front.companyTrendList",
    "companyDateTrendList": "/?action=front.companyDateTrendList",
    "updateCompanyGroup": "/?action=front.updateCompanyGroup",
    "loo_companyList": "?action=front.companyList",
    "moveSubCompany": "?action=front.moveSubCompany",
    "matchCompany": "?action=front.matchCompany"

    # 用户信息

}
