# service_test_url = "https://jiweidev.jiweinet.com/"
service_test_url = "https://www.laoyaoba.com/"
# service_test_jobs = "https://jiweidev.jiweinet.com/"
service_test_jobs = "https://www.laoyaoba.com/"
service_test_videoquote = 'https://devvp.ijiwei.com/'
service_test_ai = 'https://ai.laoyaoba.com/'

service_interfacePath = {
    # 集微GPT JIWEIGPT
    "resource": "/api/aigc/resource",  # 获取集微相关资源信息

    # 资讯相关接口news
    "lateststream": "/api/news/lateststream",  # 获取最新资讯
    "authorrecent": "/api/news/authorrecent",  # 获取作者资讯
    "feedstream": "/api/news/feedstream",  # 获取feed流接口
    "newsdetail": "/api/news/newsdetail",  # 获取资讯详情接口
    "vipfeed": "/api/news/vipfeed",  # 资讯改版-VIP资讯流
    "curlpdf": "/api/news/curlpdf",  # 请求资讯详情PDF附件接口
    "audiolist": "/api/special/audiolist",  # 获取音频专题所有数据接口
    "authorresource": "/api/news/authorresource",  # 视频详情页作者内容推荐接口
    "detailssimple": "/api/news/detailssimple",  # 请求资讯详情实时更新数据
    "newsorder": "/api/news/newsorder",  # 付费资讯下单生成订单接口
    "audiotop": "/api/special/audiotop",  # 获取音频专题推荐置顶数据接口
    "hotlabel": "api/news/hotlabel",  # 获取热门标签接口
    "newsv3": "api/search/newsv3",  # 资讯搜索接口
    "videoadvise": "api/news/videoadvise",  # 资讯改版-视频推荐
    "viprecord": "/api/news/viprecord",  # 资讯改版-VIP浏览记录

    # 登录
    "login": "/api/login/submitcode",
    "CVdeliver": "/api/cv/deliver",  # 投递简历
    "CVmyresume": "/api/cv/myresume",  # 添加编辑我的简历信息
    "CVdetail": "/api/cv/detail",  # 获取我的简历详情信息
    "CVsearchscreen": "/api/cv/searchscreen",  # 获取屏蔽公司
    "CVsetscreen": "/api/cv/setscreen",  # 设置屏蔽公司
    "CVscreenlist": "/api/cv/screenlist",  # 屏蔽公司列表
    "CVsetstatus": "/api/cv/setstatus",  # 设置修改我的简历状态
    "CVformresource": "/api/cv/formresource",  # 获取简历相关静态资源信息
    "CVdelres": "/api/cv/delres",  # 删除简历模板接口

    "CVmyfeedback": "/api/cv/myfeedback",  # 获取简历投递个人反馈
    "CVmyinterview": "/api/cv/myinterview",  # 获取面试邀请详情
    "CVmyseedme": "/api/cv/myseedme",  # 获取简历投递关于我
    "interviewHandleview": "/api/interview/handleview",  # 更改面试邀约状态
    "CVnoticebar": "/api/cv/noticebar",  # 简历消息栏信息下发

    "CVreadnotice": "/api/cv/readnotice",  # 读取简历消息

    "CVreadnotice": "/api/cv/readnotice",  # 简历消息栏信息下发

    "mobilePhonecode": "/api/mobile/phonecode",  # 扫码登录-获取验证码
    "mobileCheckcode": "/api/mobile/checkcode",  # 扫码登录，验证码验证
    "authAuthorization": "/api/auth/authorization",  # 基础授权登录
    "authExchange": "/api/auth/exchange",  # 通过 Exchange Code 交换用户基础信息
    "liveLivefeed": "/api/live/livefeed",  # 直播列表数据流
    "airteachinLabels": "/api/airteachin/labels",  # 获取空中宣讲会label标签
    "airteachinLivefeed": "/api/airteachin/livefeed",  # 获取空中宣讲会列表
    "distinguishedData": "/api/distinguished/data",  # 获取嘉宾
    "liveWelcome": "/api/live/welcome",  # 付费引导页直播授权接口
    "liveConfirmattempt": "/api/live/confirmattempt",  # 试看token消费接口
    "livePing": "/api/live/ping",  # 直播单点登录检测接口
    "liveChatrecord": "/api/live/chatrecord",  # 获取直播间聊天记录
    "liveHeat": "/api/live/heat",  # 资讯改版V1-获取直播热数据
    "liveStatus": "/api/live/status",  # 资讯改版V1-获取直播当前状态

    "meetingpush": "/api/meeting/meetingpush",  # 成功提交学生论文
    # 会议相关接口meeting
    "agendameeting": "/api/meeting/agendameeting",  # 查询当前会议议程
    "meetingform": "/api/meeting/meetingform",  # 显示学术会议查询投稿详情及最近投稿记录
    "meetinglearning": "/api/meeting/meetinglearning",  # 显示学术会议投稿记录
    "usermeeting": "/api/user/usermeeting",  # 查询我的会议
    "meetingdetail": "/api/meeting/meetingdetail",  # 查询会议详情
    "meetingdetailinside": "/api/meeting/meetingdetailinside",  # 查询会议详情（部内使用）
    "userrelease": "/api/user/userrelease",  # 获取票卷信息
    "orderConfirm": "/api/order/confirm",  # 生成会议订单
    "meetingalterup": "/api/meeting/meetingalterup",  # 修改会议报名信息接口
    "meetingconfirm/": "/admin/meeting/confirm/",  # 获取会议订单的编号id
    "orderinvoice": "/api/order/orderinvoice",  # 我的会议页提交发票
    "suborder": "/api/order/suborder",  # 分析师会议报名
    "meetingsummit": "/api/meeting/meetingsummit",  # 峰会宝（查询/报名）接口
    "meetingschedule": "/api/meeting/meetingschedule",  # 峰会宝查询展厅权限
    "author": "/api/author/author",  # 资讯改版-统计作者信息
    "getapplythird": "/api/apply/getapplythird",  # 第三方报名表单的数据
    "getapply": "/api/apply/getapply",  # 查询会议的报名项
    "getapplypark": "/api/apply/getapplypark",  # 园区报名表单数据接口
    "getapplyschool": "/api/apply/getapplyschool",  # 学校报名表单数据接口
    "getforminfo": "/api/apply/getforminfo",  # 报名表单数据接口
    "meetingenroll": "/api/meeting/meetingenroll",  # 编辑时查询会议的报名项

    # 简历附件
    "CVupannex": "/api/cv/upannex",  # 新增个人简历附件
    "CVattach": "/api/cv/attach",  # 获取个人附件简历详情
    "CVdelattach": "/api/cv/delattach",  # 删除个人简历附件
    "CVattaches": "/api/cv/attaches",  # 获取个人附件简历列表
    "CVupattachactive": "/api/cv/upattachactive",  # 设置默认附件简历

    # 职位相关 position1
    "formresource": "/api/position/formresource",  # 获取职场相关静态资源信息
    "moreposition": "/api/position/moreposition",  # 更多职位详情
    "jobs": "/api/position/jobs",  # 首页搜索接口
    # 双选会 career_share
    "company": "/api/position/company",  # 获取双选会公司列表接口新
    "company2": "/api/position/company2",  # 获取双选会公司列表接口
    "companyschoolrecruitlive": "/api/position/companyschoolrecruitlive",  # 获取双选会校招相关信息
    "activityinfo": "/api/jobshare/activityinfo",  # 获取双选会活动详情
    "siteslist": "/api/jobshare/siteslist",  # 获取双选会场次列表
    "sitesinfo": "/api/jobshare/sitesinfo",  # 场次详细信息
    "companylist": "/api/jobshare/companylist",  # 获取场次公司列表
    # 简历附件（cv_attachment）
    "upannex": "/api/cv/upannex",  # 新增个人简历附件
    "attach": "/api/cv/attach",  # 获取个人附件简历详情
    "delattach": "/api/cv/delattach",  # 删除个人简历附件
    "attaches": "/api/cv/attachesh",  # 获取个人附件简历列表
    "upattacheactive": "/api/cv/upattacheactive",  # 设置默认附件简历
    # 学校相关
    "matchschool": "/api/cv/matchschool",  # 匹配学校名称
    "matchschoolmajor": "/api/cv/matchschoolmajor",  # 匹配学校专业信息
    # 广告管理
    "slideshow": "/api/advert/slideshow",  # 新版广告列表接口
    "slideshowclick": "/api/advert/slideshowclick",  # 增加广告的点击数
    "slideshowinfo": "/api/advert/slideshowinfo",  # 获取广告详情信息

    # 报名模板
    "applypage": "/api/position/applypage",  # 招聘活动中的报名表单页面数据接口
    "userfair": "/api/position/userfair",  # 用户获取报名活动信息列表
    "applyfair": "/api/position/applyfair",  # 招聘活动报名接口

    # 新版投票活动

    "voteactivity": "/api/vote/voteactivity",  # 获取某个投票活动的基础设施信息
    "votecompany": "/api/vote/votecompany",  # 获取某个投票活动下的参赛企业列表
    "voteranking": "/api/vote/voteranking",  # 获取某个投票活动下的排行榜单
    "companydetails": "/api/vote/companydetails",  # 获取某个投票活动中企业的详细信息
    "votepush": "/api/vote/votepush",  # 给某个投票活动中企业的投票

    # 客户模块
    "companies": "/api/v2/customer/companies",  # 获取千企名录列表
    "customer_company": "/api/v2/customer/company",  # 获取企业详情
    "products": "/api/v2/customer/company/products",  # 获取企业产品列表

    # 职位模块
    "positions": "/api/v2/positions",  # 获取职位列表

    # 幻灯片相关接口
    "slideitem": "/api/slide/slideitem",  # 获取特定条件下的数据接口

    # 宣讲会 career_talk
    "careertalklist": "/api/jobcareertalk/careertalklist",  # 宣讲会列表
    "customers": "/api/jobcareertalk/customers",  # 千企名录
    "livedetail": "/api/jobcareertalk/livedetail",  # 宣讲会直播详情
    "livecompanyposition": "/api/jobcareertalk/livecompanyposition",  # 宣讲会直播中企业和职位
    "companycareertalklist": "/api/jobcareertalk/companycareertalklist",  # 获取公司下更多的宣讲会信息

    # 我的相关 user1
    "collectjob": "api/user/mycollection",  # 我的收藏列表
    "setavatar": "/api/user/setavatar",  # 上传图片

    # 视频报价相关接口
    "samplelist": "/api/videoquote/samplelist",  # 获取样片列表
    "toplist": "/api/videoquote/toplist",  # 获取样片TOP展示
    "sampleinfo": "/api/videoquote/sampleinfo",  # 获取样片详情
    "samplelabels": "/api/videoquote/samplelabels",  # 获取样片分类
    "offer": "/api/videoquote/offer",  # 一键报价
    "formdata": "/api/videoquote/formdata",  # 获取表单
    "packagelist": "/api/videoquote/packagelist",  # 获取套餐列表
    "packageinfo": "/api/videoquote/packageinfo",  # 套餐详情
    "permission": "/api/videoquote/permission",  # 获取是否有查看行业样片权限
    "videonewslist": "/api/videoquote/videonewslist",  # 获取视频咨询列表
    "videonewsinfo": "/api/videoquote/videonewsinfo",  # 获取视频咨询详情

    # 芯力量
    "organizations": "/api/investment/organizations",  # 芯力量投资机构列表接口
    "organizationinfo": "/api/investment/organizationinfo",  # 芯力量投资机构详情接口
    "projectnews": "/api/investment/projectnews",  # 芯力量专题资讯
    "Items": "/api/investment/Items",  # 芯力量项目列表接口
    "iteminfo": "/api/investment/iteminfo",  # 芯力量项目详情
    "additem": "/api/investment/additem",  # 芯力量项目提交
    "caselist": "/api/investment/caselist",  # 芯力量成功案例列表
    "caseinfo": "/api/investment/caseinfo",  # 芯力量成功案例详情
    "checksharestatus": "/api/investment/checksharestatus",  # 检测分享状态

    # 知识付费
    "getinvoiceinfo": "/api/apply/getinvoiceinfo",  # 获取发票验证信息
    "myreport": "/api/report/myreport",  # 获取我的全部报告
    "reportinfo": "/api/report/reportinfo",  # 获取报告的详情
    "checkoutorder": "/api/report/checkoutorder",  # 生成报告订单
    "reportpay": "/api/report/reportpay",  # 报告订单付款
    "ordersofreport": "/api/report/ordersofreport",  # 获取某一个报告产生的全部订单
    "applyGetinvoiceinfo": "/api/apply/getinvoiceinfo",  # 获取发票验证信息
    "reportinvoice": "/api/order/reportinvoice",  # 开具发票
    "searchreport": "/api/report/searchreport",  # 获取报告列表
    "newsShare": "/api/news/share",  # 获取我的全部报告
    "usageofreport": "/api/report/usageofreport",  # 报告查阅密钥申请（剩余浏览次数查询）
    "browsereportbycredentials": "/api/report/browsereportbycredentials",  # 报告PDF内容获取（使用密钥）
    "reportorderdetail": "/api/report/reportorderdetail",  # 生成报告订单

    # 爱集微英文官网接口
    "order": "/api/subscribe/order",  # 订阅接口
    "active": "/api/subscribe/active",  # 激活邮件
    "cancel": "/api/subscribe/cancel",  # 取消订阅
    "enfeedstream": "/api/news/enfeedstream",  # 英文资讯Feed流接口
    "enrelevant": "/api/subscribe/enrelevant",  # 英文咨询标签相关接

    # 舆情相关 sentiment
    "sentimentlogin": "/api/sentiment/login",  # 舆情登录
    "setconfig": "/api/sentiment/setconfig",  # 设置舆情配置信息
    "configinfo": "/api/sentiment/configinfo",  # 获取舆情配置详情
    "hotlist": "/api/sentiment/hotlist",  # 获取行业热点列表
    "followlist": "/api/sentiment/followlist",  # 获取舆情关注列表
    "newskeywords": "/api/sentiment/newskeywords",  # 获取资讯标签
    "sentimentinfo": "/api/sentiment/info",  # 获取舆情文章详情

    # IC课程
    "courseinfo": "/api/course/info",  # 获取课程详情
    "coursecheckoutorder": "/api/course/checkoutorder",  # 生成课程订单

    # 新闻水印
    "produce": "api/watermark/produce",  # 给新闻附件增加用户水印

    # ai-chat
    "ai": "api/chat",
    "getAiToken": "api/chat/access/token",
    "conversation": "api/chat/conversation"

}
