Information_test_url = "http://*************:18031"
Information_pro_url = "https://infoplat.ijiwei.net"
Information_login = "https://auth.ijiwei.com/pwd/login"
Information_eip = "https://eip.ijiwei.com"

Information_path = {

    # NEWS API
    "feed": "/api/news/feed",  # 资讯流
    "hot": "/api/news/hot",  # 热门文章
    "latest": "/api/news/latest",  # 最新快讯
    "detail": "/api/news/detail",  # 精选文章详情
    "report": "/api/news/report",  # 精选文章报告
    # Report API
    "search": "/api/report/search",  # ReportSearch 图文报告列表
    "url": "/api/report/",  # ReportSearch 图文报告内容
    # 
    "logo": "/api/settings/logo",  # UpdateLogo 更新logo(UpdateLogo 更新logo)
    "banner": "/api/settings/banner",  # UpdateBanner 更新banner(GetBanner 获取banner的信息)
    "preference": "/api/settings/news/preference",  # UpdateNewsPreference 更新新闻的偏好设置(GetNewsPreference 获取新闻偏好设置)
    # User Api
    "base": "/api/user/base",  # UserBaseInfo 用户基础信息

    # 首页舆情、企业库推荐接口
    "indexyq": "/api/eip/indexYq",  # 资讯流
    "indexCompanyList": "/api/eip/indexCompanyList",
    "industryReport": "/api/eip/industryReport",

    # 舆情接口
    "hotList": "/api/opinion/hotList",

    # 舆情搜索
    "yqSearch": "/api/eip/yqSearch",

    # 首页banner
    "banner": "/api/user/base",

    # 舆情监测
    "companyPlan": "/api/plan/list",

    # 贸易管制
    "tradeList": "/api/trade/list",
    "OverseaOrgList":"/api/tradeControl/OverseaOrgList",
    "controlledList":"/api/tradeControl/controlledList",
    "controlledInfo":"/api/tradeControl/controlledInfo",

    # 企业库
    "companyList": "/api/enterprise/companyList",
    "companyIntro": "/api/eip/companyIntro",
    "getCompanyInfo": "/api/enterprise/getCompanyInfo",
    "getOpinion": "/api/enterprise/getOpinion",
    "financeReport": "/api/enterprise/financeReport",
    "companyPolicyInfo": "/api/eip/companyPolicyInfo",
    "zyzblist": "/api/enterprise/zyzblist",
    "plProjectList": "/api/enterprise/plProjectList",
    "getCompanyUser": "/api/enterprise/getCompanyUser",
    "getCompanyRecruit": "/api/enterprise/getCompanyRecruit",
    "getAntitrustList": "/api/enterprise/getAntitrustList",
    "CustomerList": "/api/enterprise/CustomerList",
    "investmentList": "/api/enterprise/investmentList",
    "getKtAnnouncementList": "/api/enterprise/getKtAnnouncementList",
    "getCourtRegisterList": "/api/enterprise/getCourtRegisterList",
    "getLawSuit": "/api/enterprise/getLawSuit",
    "trademarkList": "/api/enterprise/trademarkList",
    "patentList": "/api/enterprise/patentList",
    "getOpinionPrecise": "/api/eip/getOpinionPrecise",

    # 企业项目库
    "areaRaceList": "/api/project/areaRaceList",
    "preProject": "/api/project/preProject",

    # 集微报告
    "jiweiReport": "/api/report/search",

    # 设置
    "yqPlanList": "/api/plan/list",
    "companyGroupList": "/api/front/companyGroupList",
    "yqCompanyList": "/api/front/companyList",

    # 集微网-企业洞察
    "biSearch": "/api-free-nologin/enterprise/searchTyc",

    # ai企业问答
    "aiQuestion": "/api/question",
}
