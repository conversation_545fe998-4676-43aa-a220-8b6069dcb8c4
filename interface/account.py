
#######################【emp登录账号】#################################################
#emp测试环境
emp_test = {
    "account": "ajw",
    "password": "123456cao"
}
#emp正式环境
emp= {
    "account": "jiajia",
    "password": "123456cao"
}


"""oms"""

#oms测试环境
oms_test = {
    "account": "ceshi",
    "password": "111111"
}
#oms测试环境
oms = {
    "account": "xiaoxao",
    "password": "123456"
}


# 登录用户密码
oms_account = {
    "account": "***********",
    "password": "wo94huangfei"
}
oms_account_sxd = {
    "account": "sxd",
    "password": "123456"
}
oms_account_test = {
    "account": "ceshi",
    "password": "111111"
}
oms_account_yb = {
    "account": "***********",
    "password": "wo94huangfei"
}

# 企业库登录账号
mp_account_kuu = {
    "account":"***********",
    "password":"e41f11ab5fc3e3b599e102fe0907a6f5@y",
    "remAccount": "true",
    "token":"d298040da1c8cf6372ef3d98dc079c7e"
    }




emp_account = {
    "account": "***********",
    "password": "wo94huangfei"
}
emp_account_sxd = {
    "account": "sxd",
    "password": "sxd123"
}
emp_account_sxdlt = {
    "account": "sxdlt",
    "password": "123sxdlt"
}
emp_xiaoxiao = {
    "account": "ajw",
    "password": "123456cao"
}
emp_xiaoxiao8 = {
    "account":"ajw",
    "password":"123456cao"
    }
emp_account_liet = {
    "account": "liet",
    "password": "123456lt"
}

"""service"""
service_account_sxd = {
    "login_platform": "1",
    "password": "0d5bd268931ebca0753acb2ff54a40f3",
    "phone": "***********",
    "source": "pc",
    "token": "",
    "type": "3",
}

"""信息平台登录账号"""
infoplat_login = {
    "username": "e8a83fcb",
    "password": "2c13b4d3",
    "client_id": "3",
    "client_secret": "mmwGiXLYrr0XcSFXSTst4sDSgSRHoMx16t12gGNE",
    "scope": "eip"
}

"""签到管理平台"""
ACP_ADMIN_yb = {
    "phone": "***********",
    "password": "123456"
}


'''会议签到系统'''
ACP_checkin_token = {
    "code":"d1609ea2",
    "place_id":629,
    "account":"***********",
    "password":"wo94huangfei"
}


service_account_xiaoxiao = {
    "login_platform": "1",
    "password": "0d5bd268931ebca0753acb2ff54a40f3",
    "phone": "***********",
    "source": "pc",
    "token": "",
    "type": "3",
}

