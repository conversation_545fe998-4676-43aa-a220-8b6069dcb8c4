# 签到管理平台


ACP_ADMIN_test_url = "http://47.112.148.159:8281"

# 签到管理平台登录
ACP_ADMIN_interfacePath = {
    "login": "/api/access/token",
}

# 会议管理
management_of_meetings = {
    "list_activity": "/api/activity",  # 获取会议列表
    "create_activity": "/api/activity",  # 创建会议
    "modify_activity_info": "/api/activity/",  # 编辑会议
    "activity_info": "/api/activity/",  # 会议详情
    "release_activity_info": "/api/activity/",  # 发布会以
    "del_activity_info": "/api/activity/",  # 删除会议
    "health_check": "/api/sync-robot/health-check"  # 查看速率

}

# 场馆管理
venue_management = {
    "add_place_info_of_activity": "/api/activity/{place_id}/place",  # 添加场馆
    "modify_place_info": "/api/place/",  # 编辑场馆
    "del_place_info": "/api/place/"  # 删除场馆
}
