oms_test_url = "http://47.112.148.159:8680"  # 测试环境
# oms 登录
oms_login = {
    "login": "/api/access/token",
}
# 运营管理
oms_path = {
    "function": "/api/saas/job/config/field",  # 职能分类

}

# 宣讲会
oms_career_talk = {
    "GET_career_talk": "/api/saas/career/talk",  # 宣讲会查询
    "POST_career_talk": "/api/saas/career/talk",  # 宣讲会新增
    'PUT_career_talk': '/api/saas/career/talk/{talk_id}/status',  # 操作-上下架
    'GET_company_list': '/api/saas/career/talk/{id}/company/list',  # 操作-口播/演播名单-查询列表
    'GPT_talk__company': '/api/saas/career/talk/{id}/company',  # 操作-口播/演播名单-查询查询名单
    'POST_talk__company': '/api/saas/career/talk/{id}/company',  # 操作-口播名单-添加企业
    'DELETE_talk__company': '/api/saas/career/talk/{id}/company/392',  # 操作-口播名单-移除添加企业
    'PUT_talk__compan_top': '/api/saas/career/talk/{talk_id}/company/{company_id}/top',  # 操作-口播名单-企业置顶
    'DELETE_career_talk': '/api/saas/career/talk/{id}',  # 操作-删除
    'GET_career_talk_enroll_data': '/api/saas/apply/{unit_id}/attendee'  # 操作-报名数据
}
# 双选会
oms_jop_share = {
    "jop_share_List_of_activities": "/api/saas/job_share/sites",  # 查询双选会列表
    "jop_share_new_shuang": "/api/saas/job_share/sites",  # 新建双选会
    "job_share_Modify_the_shuang": "/api/saas/job_share/sites/",  # 修改双选会
    "job_share_shelves_shuang": "/api/saas/job_share/sites/",  # 上架
    "job_share_hot": "/api/saas/job_share/sites/{id}/hot",  # 增加热门
    "job_share_Companies_list_shuang": "/api/saas/job_share/company/list",  # 查询企业列表
    "job_share_Companies_to_add_shuang": "/api/saas/job_share/company",  # 双选会增加企业
    "job_share_Enterprise_at_the_top": "/api/saas/job_share/company/347/is_top",  # 置顶企业及取消置顶
    "job_share_List_of_Enterprises": "/api/saas/job_share/company",  # 查询双选会下的企业名单
    "job_share_Removing_an_Enterprise": "/api/saas/job_share/company",  # 移除企业
    "job_share_delete": "/api/saas/job_share/sites/",  # 删除双选会
}

# 企业客户
oms_job_customers = {

    "Job_customizer_Querying_Enterprise_Customers": "/api/saas/job/customer",  # 查询全部企业客户
    "Job_customizer_New_Enterprise": "/api/saas/job/customer",  # 新建企业客户
    "job_customers_Modify_the_Enterprise": "/api/saas/job/customer/",  # 修改企业客户信息
    "job_customers_Details_of_the_Enterprise": "/api/saas/job/customer/",  # 查看某个企业信息
    "job_customers_Placed_at_the_top": "/api/saas/job/customer/{id}/top",  # 置顶功能接口
    "job_customers_Reset_the_Password": "/api/saas/job/customer/{id}/password/reset",  # 重置企业客户密码
    "job_customers_Deleting_an_Enterprise": "/api/saas/job/customer/{delete_id}",  # 删除企业客户
}

# 轮播管理
oms_saas_slideshow = {
    'GET_saas_slideshow': '/api/saas/slideshow',  # 轮播管理-查询
    'POST_saas_slideshow': '/api/saas/slideshow',  # 轮播管理-新建轮播
    'DELETE_slideshow': '/api/saas/slideshow/{id}',  # 轮播管理-删除
    'PUT_saas_slideshow': '/api/saas/slideshow/{id}/status',  # 轮播管理-操作-上下架
    'GET_saas_slideshow_alone': '/api/saas/slideshow/{id}',  # 轮播管理-操作-查看
    'PUT_saas_slideshow_update': '/api/saas/slideshow/{id}',  # 轮播管理-操作-编辑
}

# 职位库
oms_position = {
    "position_Job_List_Query": "/api/saas/job/position",  # 职位列表查询
    "position_Job_Details_Enquiry": "/api/saas/job/position/",  # 职位详情查询
    "position_At_the_top": "/api/saas/job/position/",  # 职位置顶
    "position_Type_of_Job": "/api/saas/job/position/types",  # 获取职位类型
    "position_Resume_List": "/api/saas/job/cv",  # 职位下的简历列表

}

# oms简历库
oms_oms_cv = {
    "OMS_cv_Resume_List": "/api/saas/job/cv",  # OMS查询简历列表
    "OMS_cv_Record_of_delivery": "/api/saas/job/cv/deliver/",  # 投递记录
    "OMS_cv_Resume_Details": "/api/saas/job/cv/",  # 查看个人简历详情
}
