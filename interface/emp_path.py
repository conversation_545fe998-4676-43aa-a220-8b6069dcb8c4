emp_test_url = "http://47.112.148.159:8780"  # 测试环境
emp_url = "http://emp.laoyaoba.com/"  # 正式环境
emp_path = {
    # 登录
    "login": "/api/access/token",
    # 个人中心模块接口 personal
    "company1": "/api/personal/company",  # 查看当前用户所在公司信息
    "account": "/api/personal/account",  # 修改个人信息
    "updatecompany": "/api/personal/company",  # 修改当前用户所在公司信息

    # 猎头公司模块 position1:company
    "company": "/api/position/company",  # 猎头用户新增维护的公司
    "companylist": "/api/position/company",  # 获取维护的公司信息列表
    "companyinfo": "/api/position/company/{346}",  # 获取维护的公司信息详情
    # 职位管理模块 position1
    "position1": "/api/position",  # 添加职位
    "positionid": "/api/position/",  # 修改职位
    "positionlist": "/api/position",  # 获取职位列表
    "positioncomment": "/api/position/",  # 获取职位详情旧
    "positioncommentid": "/api/position/",  # 获取职位详情新
    "positionrefresh": "/api/position/",  # 刷新职位信息
    "positioncollect": "/api/position/cv/collect",  # 添加取消收藏
    "positioncitylist": "/api/position/citylist",  # 获取所拥有的职位所在城市

    # 简历相关接口cv
    "reportIndex": "/api/report/index",  # 获取首页数据报表基础信息 report
    "personalAccount": "/api/personal/account",  # 修改个人信息成功
    "personalCompany": "/api/personal/company",  # 修改/查看当前用户所在公司信息成功
    "noticeTitle": "/api/notice/title",  # 获取标题信息通知接口
    "CVsuitable": "/api/cv/suitable",  # 标记简历状态

    "createPosition": "/api/position",  # 添加职位
    "positionCVcollect": "/api/position/cv/collect",  # 添加/取消简历收藏
    "CVcollect": "/api/cv/collect",  # 我的收藏列表
    "CVdeliverstatus": "/api/cv/deliverstatus",  # 简历控制详情
    "CVdownloadtask": "/api/cv/download/task",  # 批量导出简历
    "CVinvited": "/api/cv/invited",  # 添加面试邀约
    "CVinvitedinfo": "/api/cv/invited/info",  # 获取面试邀约详情
    "CV": "/api/cv/",  # 查看单个简历详细信息接口

    # 新调试
    "download": "/api/cv/download",  # 下载的简历列表、搜索接口
    "downloadid": "/cv/download*/{id}*",  # 下载附件简历
    "list": "/api/cv/list",  # 简历列表

    # 找回密码相关（pwd）
    "code": "/api/pwd/send-security-code",  # 发送验证码
    "reset": "/api/pwd/reset",  # 重置密码

    # 首页内容相关接口
    "feedback": "/api/feedback/add",  # 提交意见反馈接口 feedback
    "index": "/api/report/index",  # 获取首页数据报表基础信息 report

}
