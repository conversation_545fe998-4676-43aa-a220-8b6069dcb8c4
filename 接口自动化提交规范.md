# 接口自动化测试提交规范 📋

## 📖 项目概述

本项目是一个基于 pytest 的接口自动化测试框架，专为 API 测试设计，具有简洁的错误报告、智能测试重试机制和高效的数据驱动能力。框架采用分层架构设计，支持多系统模块的接口测试。

### 核心特性
- 🚀 **性能优化**：JSON文件处理优化、HTTP请求缓存、测试执行优化
- 📊 **简洁错误报告**：自定义 pytest 插件提供清晰的错误摘要
- 🔄 **智能重试机制**：支持失败测试的自动重试，避免不必要的重试
- 📈 **数据驱动测试**：从 JSON 文件加载测试数据，实现数据与逻辑分离
- 📝 **Allure 报告集成**：自动生成 HTML 格式的测试报告

## 🌳 Git 工作流程规范

### 分支管理策略

#### 主要分支
- **`main`** - 主分支，保持稳定可发布状态
- **`develop`** - 开发分支，集成最新开发功能
- **`feature/*`** - 功能分支，开发新功能或接口测试
- **`bugfix/*`** - 修复分支，修复测试用例或框架问题
- **`hotfix/*`** - 热修复分支，紧急修复生产问题

#### 分支命名规范
```bash
# 功能分支
feature/接口模块名-功能描述
feature/user-login-api
feature/order-management-api

# 修复分支
bugfix/模块名-问题描述
bugfix/auth-token-expired
bugfix/data-validation-error

# 热修复分支
hotfix/紧急问题描述
hotfix/critical-api-failure
```

### 提交信息规范

#### 提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- **feat**: 新增接口测试用例或功能
- **fix**: 修复测试用例或框架问题
- **docs**: 文档更新
- **style**: 代码格式调整（不影响功能）
- **refactor**: 代码重构
- **test**: 测试相关修改
- **chore**: 构建过程或辅助工具的变动

#### 提交信息示例
```bash
feat(user): 新增用户登录接口测试用例

- 添加用户登录成功场景测试
- 添加用户登录失败场景测试
- 更新测试数据文件

Closes #123

fix(auth): 修复token过期导致的测试失败

- 更新token获取逻辑
- 增加token有效性检查
- 优化认证错误处理

test(order): 增加订单管理接口边界值测试

- 添加订单金额边界值测试
- 添加订单状态异常测试
- 更新断言逻辑
```

## 🔧 接口测试开发规范

### 新增接口测试标准流程

#### 1. 接口定义层 (interface)
在对应的接口定义文件中添加新接口路径：

```python
# 例如在 interface/service_path.py 中添加新接口
service_interfacePath = {
    # 现有接口...
    
    # 新增接口 - 使用清晰的命名
    "user_profile": "/api/user/profile",
    "order_create": "/api/order/create",
    "payment_process": "/api/payment/process",
}
```

#### 2. 数据层 (data)
创建测试数据文件，遵循标准结构：

```json
{
  "testcases": [
    {
      "name": "测试用例描述",
      "id": "唯一标识符",
      "parameters": {
        "请求参数": "参数值"
      },
      "desired": {
        "期望结果": "期望值"
      }
    }
  ]
}
```

#### 3. 测试用例层 (testcase)
创建测试用例文件，遵循标准模板：

```python
import allure
import pytest
from common.read_jsonfile_utils import ReadJsonFileUtils
from common.request import KeyDemo
from common.util.assert_type import equals
from interface import service_path

# 获取测试数据
data_file_path = ReadJsonFileUtils.get_data_path("data/模块名", "测试数据文件.json")
param_data = ReadJsonFileUtils(data_file_path)
data_item = param_data.get_value('testcases')

@allure.feature("功能模块名")
class TestApiName:
    @pytest.mark.smoke
    @pytest.mark.parametrize("test_data", data_item[0:1])
    def test_api_function(self, test_data, headers):
        """测试用例描述"""
        res = KeyDemo.my_request(
            method='get',
            url=service_path.service_test_url + service_path.service_interfacePath["api_name"],
            headers=headers,
            params=test_data['parameters']
        )
        
        # 断言验证
        expected_status = test_data['desired']["status"]
        equals(expected_status, res["status"])
```

### 文件命名规范

#### 目录结构规范
```
testcase/
├── 系统模块名/
│   ├── 功能模块名/
│   │   ├── test_具体功能.py
│   │   └── get_headers.py (认证相关)
│   └── __init__.py

data/
├── 系统模块名/
│   ├── 功能模块名/
│   │   └── test_data_具体功能.json
│   └── __init__.py

interface/
├── 系统模块名_path.py
└── __init__.py
```

#### 文件命名规则
- **测试文件**: `test_功能名.py`
- **数据文件**: `test_data_功能名.json`
- **接口定义**: `系统名_path.py`
- **认证文件**: `get_headers.py` 或 `token.py`

### 测试用例编写规范

#### 必须包含的元素
1. **Allure 标记**: `@allure.feature("功能模块")`
2. **pytest 标记**: `@pytest.mark.smoke` 或其他标记
3. **参数化测试**: `@pytest.mark.parametrize`
4. **清晰的测试方法名**: `test_功能_场景`
5. **详细的文档字符串**: 描述测试目的和预期结果

#### 断言规范
使用框架提供的断言方法：
```python
from common.util.assert_type import (
    equals, not_equals, contains, greater_than, 
    less_than, length_equals, startswith, endswith
)

# 基本断言
equals(expected_value, actual_value, "错误信息")

# 数值比较
greater_than(actual_count, 0, "数据量应大于0")

# 字符串断言
contains(response_text, "success", "响应应包含成功标识")
```

#### 错误处理规范
```python
# 使用框架的错误检查
from common.util.assert_type import assert_no_error_indicators_in_response

# 检查响应中是否包含错误指示符
assert_no_error_indicators_in_response(response_text, "API响应检查")
```

## 📊 代码质量要求

### 代码风格规范

#### Python 代码规范
- 遵循 PEP 8 编码规范
- 使用 4 个空格缩进
- 行长度不超过 120 字符
- 类名使用 PascalCase
- 函数名和变量名使用 snake_case
- 常量使用 UPPER_CASE

#### 注释规范
```python
def test_user_login_success(self, login_data, headers):
    """
    测试用户登录成功场景
    
    Args:
        login_data: 登录测试数据
        headers: 请求头信息
        
    Expected:
        - 返回状态码为 0
        - 返回用户token
        - 用户信息完整
    """
    pass
```

### 测试覆盖率要求

#### 接口测试覆盖率
- **正常场景**: 必须覆盖所有正常业务流程
- **异常场景**: 覆盖主要异常情况（参数错误、权限不足等）
- **边界值测试**: 覆盖参数边界值情况
- **性能测试**: 关键接口需要性能测试

#### 测试标记使用
```python
@pytest.mark.smoke      # 冒烟测试
@pytest.mark.regression # 回归测试
@pytest.mark.critical   # 关键功能测试
@pytest.mark.slow       # 慢速测试
@pytest.mark.flaky      # 不稳定测试（需要重试）
```

### 性能要求

#### 测试执行性能
- 单个测试用例执行时间不超过 30 秒
- 超过 3 秒的测试会被标记并报告
- 使用缓存机制避免重复的 API 调用
- 合理使用测试夹具 (fixture) 优化执行速度

#### 资源使用规范
```python
# 使用会话级别的 fixture 避免重复认证
@pytest.fixture(scope="session")
def auth_headers():
    return get_auth_token()

# 使用缓存避免重复数据加载
@pytest.fixture(scope="module")
def test_data():
    return load_test_data()
```

## 📁 测试数据管理规范

### 数据文件组织

#### 目录结构
```
data/
├── 系统模块名/
│   ├── 功能模块名/
│   │   ├── 正常场景数据.json
│   │   ├── 异常场景数据.json
│   │   └── 边界值数据.json
│   └── common/
│       └── 公共数据.json
```

#### 数据文件命名
- **功能测试**: `test_data_功能名.json`
- **异常测试**: `test_data_功能名_error.json`
- **边界值测试**: `test_data_功能名_boundary.json`
- **性能测试**: `test_data_功能名_performance.json`

### JSON 数据结构规范

#### 标准数据结构
```json
{
  "testcases": [
    {
      "name": "测试用例名称",
      "id": "唯一标识符",
      "description": "测试用例描述",
      "parameters": {
        "请求参数名": "参数值"
      },
      "headers": {
        "Content-Type": "application/json"
      },
      "desired": {
        "status": 0,
        "message": "success"
      },
      "assertions": [
        {
          "field": "data.count",
          "operator": "greater_than",
          "value": 0
        }
      ]
    }
  ]
}
```

#### 数据管理最佳实践
- 使用有意义的测试用例名称
- 提供清晰的测试用例描述
- 参数值使用真实有效的数据
- 避免硬编码敏感信息
- 使用环境变量管理配置信息

## 🔄 CI/CD 集成规范

### 自动化测试执行

#### 测试执行策略
```bash
# 冒烟测试 - 每次提交后执行
pytest testcase/ -m smoke --alluredir=reports/smoke

# 回归测试 - 每日定时执行
pytest testcase/ -m regression --alluredir=reports/regression

# 全量测试 - 发布前执行
pytest testcase/ --alluredir=reports/full
```

#### 测试报告生成
```bash
# 生成 Allure 报告
allure generate reports/json -o reports/html --clean

# 启动报告服务
allure serve reports/json
```

### 质量门禁

#### 提交前检查
- [ ] 所有新增测试用例通过
- [ ] 代码符合规范要求
- [ ] 测试覆盖率达标
- [ ] 性能测试通过
- [ ] 文档更新完整

#### 合并前检查
- [ ] 代码审查通过
- [ ] 冒烟测试通过
- [ ] 集成测试通过
- [ ] 无安全漏洞
- [ ] 无性能回归

## 👥 代码审查流程

### 审查标准

#### 代码质量检查
1. **功能正确性**: 测试逻辑正确，覆盖预期场景
2. **代码规范**: 符合项目编码规范
3. **性能考虑**: 无明显性能问题
4. **安全性**: 无敏感信息泄露
5. **可维护性**: 代码结构清晰，易于维护

#### 测试用例审查
1. **测试设计**: 测试场景完整，边界值覆盖
2. **数据质量**: 测试数据真实有效
3. **断言充分**: 断言覆盖关键验证点
4. **错误处理**: 异常场景处理完善
5. **文档完整**: 注释和文档清晰

### 审查流程

#### Pull Request 流程
1. **创建 PR**: 提供清晰的 PR 描述和变更说明
2. **自动检查**: CI/CD 自动执行测试和质量检查
3. **代码审查**: 至少一名团队成员进行代码审查
4. **修改完善**: 根据审查意见修改代码
5. **最终确认**: 审查通过后合并到目标分支

#### 审查清单
```markdown
## 代码审查清单

### 功能性
- [ ] 测试用例逻辑正确
- [ ] 覆盖主要业务场景
- [ ] 异常处理完善

### 代码质量
- [ ] 符合编码规范
- [ ] 命名清晰有意义
- [ ] 注释充分准确

### 性能
- [ ] 无明显性能问题
- [ ] 合理使用缓存
- [ ] 避免重复调用

### 安全性
- [ ] 无敏感信息泄露
- [ ] 输入验证充分
- [ ] 权限控制正确

### 可维护性
- [ ] 代码结构清晰
- [ ] 易于扩展修改
- [ ] 文档更新及时
```

## 📝 提交检查清单

### 提交前自检
- [ ] 本地测试全部通过
- [ ] 代码符合规范要求
- [ ] 提交信息格式正确
- [ ] 相关文档已更新
- [ ] 无调试代码残留

### 文件检查
- [ ] 测试文件命名规范
- [ ] 数据文件结构正确
- [ ] 接口定义完整
- [ ] 依赖关系清晰

### 质量检查
- [ ] 测试覆盖率达标
- [ ] 性能要求满足
- [ ] 错误处理完善
- [ ] 日志信息充分

---

## 📞 联系方式

如有疑问或建议，请联系测试团队或在项目中创建 Issue。

**遵循规范，保证质量，持续改进！** 🚀
