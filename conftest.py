from logging import Logger

import pytest, os
import time
import sys
import warnings

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import our custom plugin
pytest_plugins = ["common.pytest_plugins.concise_errors"]

# Filter out the urllib3 deprecation warning
warnings.filterwarnings("ignore", message="'urllib3\\[secure\\]' extra is deprecated")

log = Logger(str(os.path.basename(__file__)))
from common.header import getServiceToken, getServiceAuthorization, getAuthorization
from interface import account


@pytest.fixture(scope='function')
def serviceToken_sxd():
    token = getServiceToken('service', account.service_account_sxd)
    return token


@pytest.fixture(scope='function')
def serviceToken():
    token = getServiceToken('service', account.service_account_xiaoxiao)
    return token


@pytest.fixture(scope='function')
def serviceAuthorization_sxd():
    authorization = getServiceAuthorization('service', account.service_account_sxd)
    return authorization


@pytest.fixture(scope='function')
def empAuthorization_xiaoxiao():
    authorization = getAuthorization('emp1', account.emp_xiaoxiao)
    return authorization


@pytest.fixture(scope='function')
def empAuthorization_sxdlt():
    authorization = getAuthorization('emp1', account.emp_account_sxdlt)
    return authorization



@pytest.hookimpl(tryfirst=True)
def pytest_runtest_setup(item):
    item.start_time = time.time()

@pytest.hookimpl(trylast=True)
def pytest_runtest_teardown(item, nextitem):
    duration = time.time() - item.start_time
    # Only print duration if it exceeds threshold to avoid duplicate output
    if duration > 3:
        print(f"\nTest {item.name} took {duration:.3f} seconds (exceeded 3s)\n")
